"""
Pydantic models for API requests and responses.
"""

from pydantic import BaseModel
from typing import Optional, List, Dict, Any

class ChatRequest(BaseModel):
    """Request model for chat interactions."""
    message: str
    session_id: Optional[str] = None

class ChatResponse(BaseModel):
    """Response model for chat interactions."""
    response: str
    session_id: str
    suggested_actions: Optional[List[Dict[str, Any]]] = None
