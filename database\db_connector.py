"""
MongoDB connector for ReAct Agent memory storage.
This module handles connection and operations for persistent agent memories.
"""

import os
import logging
import pymongo
import pendulum
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger("DBConnector")

class DBConnector:
    """
    MongoDB connector for storing and retrieving agent memories.
    """
    
    def __init__(self, mongo_uri: Optional[str] = None, db_name: str = "hospital_agent"):
        """
        Initialize MongoDB connection.
        
        Args:
            mongo_uri: MongoDB connection URI (defaults to env var MONGO_URI)
            db_name: MongoDB database name
        """
        # Load environment variables
        load_dotenv()
        
        # Use provided URI or get from environment variables
        self.mongo_uri = mongo_uri or os.getenv("MONGO_URI", "mongodb://localhost:27017/")
        self.db_name = db_name
        self.client = None
        self.db = None
        
        # Initialize collections
        self.memories_collection = None
        self.sessions_collection = None
        
        # Connect to MongoDB
        self._connect()
        
    def _connect(self) -> None:
        """
        Establish connection to MongoDB.
        """
        try:
            self.client = pymongo.MongoClient(self.mongo_uri)
            self.db = self.client[self.db_name]
            
            # Set up collections
            self.memories_collection = self.db["memories"]
            self.sessions_collection = self.db["sessions"]
            
            # Create indexes
            self.memories_collection.create_index([("session_id", pymongo.ASCENDING)])
            self.memories_collection.create_index([("created_at", pymongo.DESCENDING)])
            self.memories_collection.create_index([("memory_type", pymongo.ASCENDING)])
            self.sessions_collection.create_index([("session_id", pymongo.ASCENDING)])
            
            logger.info(f"Connected to MongoDB at {self.mongo_uri}")
            
        except Exception as e:
            logger.error(f"MongoDB connection error: {str(e)}")
            # Continue without database if connection fails
            self.client = None
            self.db = None
            
    def is_connected(self) -> bool:
        """
        Check if MongoDB connection is active.
        
        Returns:
            Boolean indicating if connection is active
        """
        if not self.client:
            return False
            
        try:
            # Ping the database to check connection
            self.client.admin.command('ping')
            return True
        except Exception:
            return False
            
    def create_session(self, session_id: str, user_data: Optional[Dict[str, Any]] = None) -> bool:
        """
        Create a new conversation session.
        
        Args:
            session_id: Unique identifier for the session
            user_data: Optional metadata about the user/session
            
        Returns:
            Boolean indicating success
        """
        if not self.is_connected():
            logger.warning("MongoDB not connected, session not created")
            return False
            
        try:
            session_data = {
                "session_id": session_id,
                "created_at": pendulum.now("UTC"),
                "last_activity": pendulum.now("UTC"),
                "memory_count": 0
            }
            
            # Add user data if provided
            if user_data and isinstance(user_data, dict):
                session_data.update(user_data)
                
            # Insert session document
            self.sessions_collection.insert_one(session_data)
            logger.info(f"Created new session: {session_id}")
            return True
        except Exception as e:
            logger.error(f"Error creating session: {str(e)}")
            return False
            
    def update_session_activity(self, session_id: str) -> bool:
        """
        Update the last activity timestamp of a session.
        
        Args:
            session_id: Unique identifier for the session
            
        Returns:
            Boolean indicating success
        """
        if not self.is_connected():
            return False
            
        try:
            self.sessions_collection.update_one(
                {"session_id": session_id},
                {"$set": {"last_activity": pendulum.now("UTC")}}
            )
            return True
        except Exception as e:
            logger.error(f"Error updating session activity: {str(e)}")
            return False
            
    def store_memory(self, session_id: str, memory_type: str, content: str, 
                     importance: int = 1, metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Store a memory in the database.
        
        Args:
            session_id: Session identifier
            memory_type: Type of memory (specialty, appointment, user_info, etc)
            content: The actual memory content
            importance: Importance score (higher means more important)
            metadata: Additional data about the memory
            
        Returns:
            Memory ID if successful, empty string otherwise
        """
        if not self.is_connected():
            logger.warning("MongoDB not connected, memory not stored")
            return ""
            
        try:
            memory_data = {
                "session_id": session_id,
                "memory_type": memory_type,
                "content": content,
                "importance": importance,
                "created_at": pendulum.now("UTC"),
                "accessed_count": 0,
                "last_accessed": None
            }
            
            # Add metadata if provided
            if metadata and isinstance(metadata, dict):
                memory_data["metadata"] = metadata
                
            # Insert memory document
            result = self.memories_collection.insert_one(memory_data)
            memory_id = str(result.inserted_id)
            
            # Update memory count in session
            self.sessions_collection.update_one(
                {"session_id": session_id},
                {"$inc": {"memory_count": 1}}
            )
            
            logger.info(f"Stored memory {memory_id} for session {session_id}")
            return memory_id
        except Exception as e:
            logger.error(f"Error storing memory: {str(e)}")
            return ""
            
    def retrieve_memories(self, session_id: str, memory_type: Optional[str] = None, 
                         limit: int = 10) -> List[Dict[str, Any]]:
        """
        Retrieve memories for a session.
        
        Args:
            session_id: Session identifier
            memory_type: Optional filter for type of memory
            limit: Maximum number of memories to retrieve
            
        Returns:
            List of memory documents
        """
        if not self.is_connected():
            logger.warning("MongoDB not connected, returning empty memories")
            return []
            
        try:
            # Build query
            query = {"session_id": session_id}
            if memory_type:
                query["memory_type"] = memory_type
                
            # Get memories sorted by importance and recency
            memories = list(self.memories_collection.find(
                query, 
                sort=[("importance", pymongo.DESCENDING), ("created_at", pymongo.DESCENDING)],
                limit=limit
            ))
            
            # Convert ObjectId to string for serialization
            for memory in memories:
                if "_id" in memory:
                    memory["_id"] = str(memory["_id"])
                    
            # Update access counts and last_accessed
            if memories:
                memory_ids = [memory["_id"] for memory in memories]
                self.memories_collection.update_many(
                    {"_id": {"$in": memory_ids}},
                    {
                        "$inc": {"accessed_count": 1},
                        "$set": {"last_accessed": pendulum.now("UTC")}
                    }
                )
                
            logger.info(f"Retrieved {len(memories)} memories for session {session_id}")
            return memories
        except Exception as e:
            logger.error(f"Error retrieving memories: {str(e)}")
            return []
            
    def delete_memory(self, memory_id: str) -> bool:
        """
        Delete a specific memory.
        
        Args:
            memory_id: ID of the memory to delete
            
        Returns:
            Boolean indicating success
        """
        if not self.is_connected():
            return False
            
        try:
            # Convert string ID to ObjectId
            object_id = pymongo.ObjectId(memory_id)
            
            # Get session_id before deletion to update count
            memory = self.memories_collection.find_one({"_id": object_id})
            if not memory:
                logger.warning(f"Memory {memory_id} not found for deletion")
                return False
                
            session_id = memory.get("session_id")
            
            # Delete the memory
            result = self.memories_collection.delete_one({"_id": object_id})
            
            if result.deleted_count > 0:
                # Decrement memory count in session
                if session_id:
                    self.sessions_collection.update_one(
                        {"session_id": session_id},
                        {"$inc": {"memory_count": -1}}
                    )
                logger.info(f"Deleted memory {memory_id}")
                return True
            else:
                logger.warning(f"Memory {memory_id} not found")
                return False
        except Exception as e:
            logger.error(f"Error deleting memory: {str(e)}")
            return False
