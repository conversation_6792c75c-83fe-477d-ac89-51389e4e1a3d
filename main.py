#!/usr/bin/env python3
"""
FastAPI server for the SmartClinic ReAct Agent.
This script creates a RESTful API around the agent for HTTP interactions.
The agent uses ReAct (Reason-Act-Observe) paradigm to handle specialty and appointment queries.
MongoDB is used for persistent conversation memory.
"""

import os
import json
import logging
import asyncio
import uuid
from dotenv import load_dotenv
from fastapi import FastAPI, Request, Form, BackgroundTasks, WebSocket, WebSocketDisconnect, HTTPException, Cookie, Depends
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from pydantic import BaseModel
import uvicorn
from react_agent import ReActAgent
from typing import Optional, List, Dict, Any
import traceback
import re

# Custom logging handler to capture logs
class WebSocketLogHandler(logging.Handler):
    def __init__(self):
        super().__init__()
        self.logs = []
        self.clients = set()
        self.loop = None  # Will store event loop reference when available
        
    def emit(self, record):
        log_entry = self.format(record)
        self.logs.append(log_entry)
        
        # Store the log but don't try to broadcast it immediately
        # Broadcasting will happen when clients connect
        # This avoids the "no running event loop" error
        
    async def broadcast(self, message):
        for client in list(self.clients):
            try:
                await client.send_text(json.dumps({"log": message}))
            except Exception:
                self.clients.remove(client)
    
    def add_client(self, websocket):
        self.clients.add(websocket)
        
    def remove_client(self, websocket):
        if websocket in self.clients:
            self.clients.remove(websocket)

# Set up logging to display all steps
ws_handler = WebSocketLogHandler()
logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger("api")
logger.addHandler(ws_handler)

# Load environment variables
load_dotenv()

# Get configuration from environment variables
llm_endpoint = os.getenv("LLM_ENDPOINT", "http://************:1234/v1/chat/completions")
specialty_api_endpoint = os.getenv("SPECIALTY_API_ENDPOINT", "http://eserver/api/his/AppointmentsAPI/InitAll")
specialty_api_token = os.getenv("SPECIALTY_API_TOKEN")
mongo_uri = os.getenv("MONGO_URI", "mongodb://localhost:27017/")

# Check if token is available
if not specialty_api_token:
    logger.warning("SPECIALTY_API_TOKEN not found in environment variables. Using default token.")
    specialty_api_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1laWRlbnRpZmllciI6IjB4MzEzNDM0MzdCMEE2RjcxQS0yRkM0LTRENTYtOTcwOS1EOTlBMjQ1REYyQkIiLCJleHAiOjE3NTMxMDE2NDAsImlzcyI6Iml2aXZhY2FyZS5jb20iLCJhdWQiOiJpdml2YWNhcmUuY29tIn0.ChI3mME-fH6aDc8aeyQY1uqnhJC4Nhe05zF1Ck5R6Pk"
# Create the ReAct Agent
agent = ReActAgent(
    llm_endpoint=llm_endpoint,
    specialty_api_endpoint=specialty_api_endpoint,
    specialty_api_token=specialty_api_token,
    mongo_uri=mongo_uri,
    debug_mode=True  # Enable debug mode to show all steps
)

# Create FastAPI app
app = FastAPI(
    title="SmartClinic ReAct Agent API",
    description="API for interacting with the hospital chatbot for specialties and appointments using ReAct paradigm",
    version="1.0.0"
)

# Define request and response models
class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    session_id: str
    suggested_actions: Optional[List[Dict[str, Any]]] = None

# Create endpoint for chat interactions
@app.post("/api/chat", response_model=ChatResponse)
async def chat(request: Request):
    try:
        # Parse the request body
        data = await request.json()
        user_message = data.get("message")
        session_id = data.get("session_id")

        if not user_message:
            return JSONResponse(
                status_code=400, 
                content={"error": "No message provided"}
            )
        
        if not agent:
            return JSONResponse(
                status_code=500, 
                content={"error": "Agent not initialized properly"}
            )

        # Generate a new session ID if not provided
        if not session_id:
            session_id = str(uuid.uuid4())
            logger.info(f"Generated new session ID: {session_id}")
            
        # Process with ReAct Agent
        try:
            # Check if this is a structured specialty selection
            parsed_message = None
            try:
                # Try to parse as JSON
                parsed_message = json.loads(user_message)
                if isinstance(parsed_message, dict) and parsed_message.get("message") == "specialty_selected":
                    # This is a structured specialty selection message
                    specialty_id = parsed_message.get("specialty_id")
                    specialty_name = parsed_message.get("specialty_name", "")
                    
                    logger.info(f"API received specialty selection: {specialty_name} (ID: {specialty_id})")
                    
                    if specialty_id:
                        # Convert the structured message to a simple text command for the agent
                        user_message = specialty_name
                        logger.info(f"API processing specialty '{specialty_name}' with ID: {specialty_id}")
                    else:
                        logger.warning("API specialty selection missing specialty_id")
            except (json.JSONDecodeError, TypeError) as e:
                # Not a structured message, continue with normal processing
                logger.info(f"API message is not structured JSON: {str(e)}")
        
            # Process the message with the agent
            response = agent.chat(user_message, session_id)
            
            # Initialize response content and suggested actions
            response_content = {
                "response": response,
                "session_id": session_id
            }
            
            suggested_actions = None
            
            # Check if the response contains specialty recommendations
            # Look for patterns like "• NEUROLOGY: " or "• **NEUROLOGY**:" in the response text
            if "please select a specialty" in response.lower() and ("• " in response or "**" in response) or "select the specialty you prefer" in response.lower():
                logger.info("Detected specialty recommendation in response")
                
                # Extract specialty recommendations from the text
                specialty_lines = []
                for line in response.split("\n"):
                    line = line.strip()
                    if line.startswith("• "):
                        specialty_lines.append(line)
                        logger.info(f"Found specialty line: {line}")
                
                logger.info(f"Found {len(specialty_lines)} specialty lines")
                
                # Extract specialties and IDs
                specialty_options = []
                # Log all specialties in memory for debugging
                logger.info(f"Specialties in memory: {agent.specialty_memory}")
                
                for line in specialty_lines:
                    # Try to extract specialty name from patterns like "• NEUROLOGY: " or "• **NEUROLOGY**:"
                    # Updated to handle mixed-case specialty names like "Accident & Emergency"
                    specialty_match = re.search(r'• (?:\*\*)?([A-Za-z][A-Za-z\s,&\']+)(?:\*\*)?:', line)
                    if specialty_match:
                        specialty_name = specialty_match.group(1).strip()
                        logger.info(f"Extracted specialty name: '{specialty_name}'")
                        
                        # Look up the specialty ID in the agent's memory
                        specialty_id = None
                        for name, id_val in agent.specialty_memory.items():
                            if name.upper() == specialty_name.upper():
                                specialty_id = id_val
                                logger.info(f"Found matching specialty in memory: {name} with ID {id_val}")
                                break
                            # Also try a case-insensitive match if the exact match fails
                            elif name.upper().strip() == specialty_name.upper().strip():
                                specialty_id = id_val
                                logger.info(f"Found case-insensitive match: {name} with ID {id_val}")
                                break
                        
                        if specialty_id:
                            specialty_options.append({
                                "label": specialty_name,
                                "value": specialty_id
                            })
                            logger.info(f"Added specialty button option: {specialty_name} (ID: {specialty_id})")
                        else:
                            logger.warning(f"Could not find ID for specialty: {specialty_name}")
                            # Try a more flexible match as a fallback
                            for name, id_val in agent.specialty_memory.items():
                                if specialty_name.upper() in name.upper() or name.upper() in specialty_name.upper():
                                    specialty_id = id_val
                                    logger.info(f"Found partial match: {name} with ID {id_val}")
                                    specialty_options.append({
                                        "label": name,  # Use the name from memory for consistency
                                        "value": specialty_id
                                    })
                                    logger.info(f"Added specialty button option via partial match: {name} (ID: {specialty_id})")
                                    break
                
                # Add buttons for specialty options if we have any valid IDs
                if specialty_options:
                    # Log all specialty options for debugging
                    logger.info(f"Final specialty options for API response: {specialty_options}")
                    
                    suggested_actions = [
                        {
                            "type": "specialty_selection",
                            "options": specialty_options
                        }
                    ]
                    logger.info(f"Added {len(specialty_options)} specialty button options to API response")
                    
                    # Add suggested actions to the response content
                    response_content["suggested_actions"] = suggested_actions
                    logger.info("Added suggested_actions to response")
            
            # Check if the response comes from recommend_specialist
            if isinstance(response, dict) and response.get("action_type") == "recommend_specialist":
                filtered_suggestions = response.get("filtered_suggestions", [])
                health_problem = response.get("health_problem", "")
                
                # Process specialty recommendations
                if filtered_suggestions:
                    # Extract specialty options
                    specialty_options = []
                    for suggestion in filtered_suggestions:
                        specialty_id = suggestion.get("specialty_id")
                        specialty_name = suggestion.get("specialty")
                        
                        if specialty_id and specialty_name:
                            specialty_options.append({
                                "label": specialty_name,
                                "value": specialty_id
                            })
                            logger.info(f"Added specialty button option: {specialty_name} (ID: {specialty_id})")
                    
                    # Add buttons for specialty options
                    if specialty_options:
                        suggested_actions = [
                            {
                                "type": "specialty_selection",
                                "options": specialty_options
                            }
                        ]
                        logger.info(f"Added {len(specialty_options)} specialty button options to API response")
                        
                        # Format the response text
                        formatted_response = f"For {health_problem}, we recommend the following specialties:"
                        response_content["response"] = formatted_response
                        response_content["suggested_actions"] = suggested_actions
            
            # Debug log the final response content
            logger.info(f"API response: {json.dumps(response_content)[:200]}...")
            
            return JSONResponse(content=response_content)
            
        except Exception as e:
            logger.error(f"Error processing chat: {str(e)}")
            logger.error(traceback.format_exc())
            return JSONResponse(
                status_code=500, 
                content={"error": f"An error occurred: {str(e)}"}
            )
    except Exception as e:
        logger.error(f"Error in API endpoint: {str(e)}")
        logger.error(traceback.format_exc())
        return JSONResponse(
            status_code=500, 
            content={"error": f"An API error occurred: {str(e)}"}
        )

# WebSocket endpoint for real-time logging
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """
    WebSocket endpoint for streaming log messages to clients
    """
    await websocket.accept()
    logger.info("WebSocket client connected")
    
    # Track session ID for this WebSocket connection
    current_session_id = None
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message_data = json.loads(data)
            user_message = message_data.get("message", "")
            session_id = message_data.get("session_id")
            
            # If no session ID provided, generate one
            if not session_id:
                session_id = str(uuid.uuid4())
                logger.info(f"Generated new WebSocket session ID: {session_id}")
            else:
                logger.info(f"Using existing WebSocket session ID: {session_id}")
                
            # Store the session ID for this connection
            current_session_id = session_id
            
            logger.info(f"Received WebSocket message for session {session_id}: {user_message}")
            
            if not user_message:
                await websocket.send_json({"error": "No message provided"})
                continue
                
            if not agent:
                await websocket.send_json({"error": "Agent not initialized properly"})
                continue
            
            # Process with ReAct Agent
            try:
                # Check if this is a structured specialty selection
                structured_message = None
                try:
                    # First try to parse the user_message as JSON
                    parsed_message = json.loads(user_message)
                    logger.info(f"Successfully parsed message as JSON: {parsed_message}")
                    
                    if isinstance(parsed_message, dict) and parsed_message.get("message") == "specialty_selected":
                        # This is a structured specialty selection message
                        specialty_id = parsed_message.get("specialty_id")
                        specialty_name = parsed_message.get("specialty_name", "")
                        
                        logger.info(f"Detected specialty selection: {specialty_name} (ID: {specialty_id})")
                        
                        if specialty_id:
                            # Convert the structured message to a simple text command for the agent
                            user_message = specialty_name
                            logger.info(f"Processing specialty '{specialty_name}' with ID: {specialty_id}")
                            
                            # Override the default date range to only use the current date
                            # Store the specialty ID for later use
                            websocket.scope["state"] = {
                                "specialty_id": specialty_id,
                                "specialty_name": specialty_name
                            }
                        else:
                            logger.warning("Specialty selection missing specialty_id")
                    elif isinstance(parsed_message, dict) and parsed_message.get("message") == "get_session_slots":
                        # This is a request for session slots
                        resource_id = parsed_message.get("resource_id")
                        session_date = parsed_message.get("session_date")
                        session_id = parsed_message.get("session_id")
                        
                        logger.info(f"Detected session slots request for resource ID: {resource_id}, date: {session_date}, session ID: {session_id}")
                        
                        if resource_id and session_date and session_id:
                            # Call the get_session_slots tool directly
                            result = agent.tools_manager.get_session_slots({
                                "resource_id": resource_id,
                                "session_date": session_date,
                                "session_id": session_id
                            })
                            
                            logger.info(f"Retrieved session slots: {json.dumps(result)[:200]}...")
                            
                            # Format the response
                            slots_count = len(result.get('slots', []))
                            slots_response = f"Available slots for the selected doctor: {slots_count} slots"
                            
                            # Send the response back to the client
                            await websocket.send_json({
                                "response": slots_response,
                                "session_id": current_session_id,
                                "slots_data": result
                            })
                            
                            logger.info(f"Sent session slots response with {slots_count} slots")
                            continue
                        else:
                            logger.warning("Session slots request missing required parameters")
                    elif isinstance(parsed_message, dict) and parsed_message.get("message") == "book_slot":
                        # This is a request to book a specific slot
                        resource_id = parsed_message.get("resource_id")
                        session_date = parsed_message.get("session_date")
                        session_id = parsed_message.get("session_id")
                        from_time = parsed_message.get("from_time")
                        
                        logger.info(f"Detected slot booking request: resource_id={resource_id}, session_date={session_date}, session_id={session_id}, from_time={from_time}")
                        
                        if all([resource_id, session_date, session_id, from_time]):
                            try:
                                # Step 1: Create walk-in appointment
                                walkin_result = agent.tools_manager.create_walkin({
                                    "resource_id": resource_id,
                                    "session_id": session_id,
                                    "session_date": session_date,
                                    "from_time": from_time
                                    # patient_id defaults to 3601 in the method
                                })
                                
                                logger.info(f"Walk-in creation result: {walkin_result}")
                                
                                # The CreateWalkin API returns the visit_id directly as integer
                                if isinstance(walkin_result, int):
                                    visit_id = walkin_result
                                    logger.info(f"Walk-in appointment created successfully. Visit ID: {visit_id}")
                                    
                                    # Step 2: Get appointment details using visit_id
                                    appointment_result = agent.tools_manager.get_appointment_number({
                                        "visit_id": str(visit_id)
                                    })
                                    
                                    logger.info(f"Appointment details result: {json.dumps(appointment_result)[:500]}...")
                                    
                                    if appointment_result and not appointment_result.get("error"):
                                        # Extract appointment ID for later use in patient journey
                                        appointment_id = None
                                        logger.info(f"Extracting appointment ID from: {json.dumps(appointment_result)[:200]}...")
                                        if appointment_result.get("appointment_data") and appointment_result["appointment_data"]:
                                            result_data = appointment_result["appointment_data"][0]
                                            logger.info(f"Result data: {result_data}")
                                            if result_data.get("RESULT"):
                                                try:
                                                    parsed_result = json.loads(result_data["RESULT"])
                                                    logger.info(f"Parsed result: {json.dumps(parsed_result)[:200]}...")
                                                    appointment_details = parsed_result.get("appointment_details", {})
                                                    logger.info(f"Appointment details: {appointment_details}")
                                                    appointment_id = appointment_details.get("identifier")
                                                    logger.info(f"Extracted appointment ID: {appointment_id}")
                                                except Exception as e:
                                                    logger.error(f"Error extracting appointment ID: {str(e)}")
                                                    logger.error(f"Raw RESULT: {result_data.get('RESULT')}")
                                        else:
                                            logger.warning("No appointment_data found in appointment result")
                                        
                                        # Send the appointment details back to the client
                                        await websocket.send_json({
                                            "response": "Your appointment has been successfully booked!",
                                            "session_id": current_session_id,
                                            "appointment_booked": True,
                                            "appointment_details": appointment_result,
                                            "visit_id": visit_id,
                                            "appointment_id": appointment_id
                                        })
                                        
                                        logger.info(f"Sent appointment booking confirmation for visit ID: {visit_id}, appointment ID: {appointment_id}")
                                        continue
                                    else:
                                        error_msg = appointment_result.get("error", "Failed to retrieve appointment details")
                                        logger.error(f"Failed to get appointment details: {error_msg}")
                                        await websocket.send_json({
                                            "response": f"Appointment was created but failed to retrieve details: {error_msg}",
                                            "session_id": current_session_id,
                                            "error": error_msg
                                        })
                                        continue
                                else:
                                    error_msg = walkin_result.get("error", "Failed to create appointment") if isinstance(walkin_result, dict) else "Failed to create appointment"
                                    logger.error(f"Failed to create walk-in appointment: {error_msg}")
                                    await websocket.send_json({
                                        "response": f"Sorry, I couldn't book your appointment: {error_msg}",
                                        "session_id": current_session_id,
                                        "error": error_msg
                                    })
                                    continue
                                    
                            except Exception as e:
                                logger.error(f"Error during appointment booking: {str(e)}")
                                await websocket.send_json({
                                    "response": f"Sorry, I encountered an error while booking your appointment: {str(e)}",
                                    "session_id": current_session_id,
                                    "error": str(e)
                                })
                                continue
                        else:
                            missing_params = [param for param, value in {
                                "resource_id": resource_id,
                                "session_date": session_date,
                                "session_id": session_id,
                                "from_time": from_time
                            }.items() if not value]
                            
                            error_msg = f"Missing required parameters for booking: {', '.join(missing_params)}"
                            logger.warning(error_msg)
                            await websocket.send_json({
                                "response": f"Sorry, I couldn't book your appointment due to missing information: {error_msg}",
                                "session_id": current_session_id,
                                "error": error_msg
                            })
                            continue
                    elif isinstance(parsed_message, dict) and parsed_message.get("message") == "get_patient_journey":
                        # This is a request to get patient journey information
                        appointment_id = parsed_message.get("appointment_id")
                        
                        logger.info(f"Detected patient journey request for appointment ID: {appointment_id}")
                        
                        if appointment_id:
                            try:
                                # Call the get_patient_journey tool with appointment ID
                                journey_result = agent.tools_manager.get_patient_journey({
                                    "appointment_id": str(appointment_id)
                                })
                                
                                logger.info(f"Patient journey result: {json.dumps(journey_result)[:500]}...")
                                
                                if journey_result and not journey_result.get("error"):
                                    # Send the patient journey data back to the client
                                    await websocket.send_json({
                                        "response": "Registration completed successfully!",
                                        "session_id": current_session_id,
                                        "patient_journey": True,
                                        "journey_data": journey_result,
                                        "appointment_id": appointment_id,
                                        "visit_id": journey_result.get("visit_id")
                                    })
                                    
                                    logger.info(f"Sent patient journey data for appointment ID: {appointment_id}")
                                    continue
                                else:
                                    error_msg = journey_result.get("error", "Failed to retrieve patient journey")
                                    logger.error(f"Failed to get patient journey: {error_msg}")
                                    await websocket.send_json({
                                        "response": f"Registration completed, but I couldn't retrieve your journey details: {error_msg}",
                                        "session_id": current_session_id,
                                        "error": error_msg
                                    })
                                    continue
                                    
                            except Exception as e:
                                logger.error(f"Error during patient journey retrieval: {str(e)}")
                                await websocket.send_json({
                                    "response": f"Registration completed, but I encountered an error retrieving your journey: {str(e)}",
                                    "session_id": current_session_id,
                                    "error": str(e)
                                })
                                continue
                        else:
                            error_msg = "Appointment ID is required for patient journey"
                            logger.warning(error_msg)
                            await websocket.send_json({
                                "response": f"Sorry, I couldn't retrieve your patient journey: {error_msg}",
                                "session_id": current_session_id,
                                "error": error_msg
                            })
                            continue
                    
                except (json.JSONDecodeError, TypeError) as e:
                    # Not a structured message, continue with normal processing
                    logger.info(f"Message is not structured JSON: {str(e)}")
                
                # Process the message with the agent
                response = agent.chat(user_message, session_id)
                
                # Initialize response content and suggested actions
                response_content = {
                    "response": response,
                    "session_id": session_id
                }
                
                suggested_actions = None
                
                # Check if the response contains specialty recommendations
                # Look for patterns like "• NEUROLOGY: " or "• **NEUROLOGY**:" in the response text
                # Check for specialty recommendations using more flexible detection
                # First make sure response is not None and is a string
                if response and isinstance(response, str) and ("please select a specialty" in response.lower() or "which specialty" in response.lower() or "select the specialty" in response.lower()) and ("• " in response or "**" in response):
                    logger.info("Detected specialty recommendation in WebSocket response")
                    
                    # Extract specialty recommendations from the text
                    specialty_lines = []
                    logger.info("Extracting specialty lines from response")
                    for line in response.split("\n"):
                        line = line.strip()
                        if line.startswith("• "):
                            specialty_lines.append(line)
                            logger.info(f"Found specialty line: {line}")
                    
                    logger.info(f"Found {len(specialty_lines)} specialty lines")
                    
                    # Extract specialties and IDs
                    specialty_options = []
                    # Log all specialties in memory for debugging
                    logger.info(f"Specialties in memory: {agent.specialty_memory}")
                    
                    for line in specialty_lines:
                        # Try to extract specialty name from patterns like "• NEUROLOGY: " or "• **NEUROLOGY**:"
                        # Updated to handle mixed-case specialty names like "Accident & Emergency"
                        specialty_match = re.search(r'• (?:\*\*)?([A-Za-z][A-Za-z\s,&\']+)(?:\*\*)?:', line)
                        if specialty_match:
                            specialty_name = specialty_match.group(1).strip()
                            logger.info(f"Extracted specialty name: '{specialty_name}'")
                            
                            # Look up the specialty ID in the agent's memory
                            specialty_id = None
                            for name, id_val in agent.specialty_memory.items():
                                if name.upper() == specialty_name.upper():
                                    specialty_id = id_val
                                    logger.info(f"Found matching specialty in memory: {name} with ID {id_val}")
                                    break
                                # Also try a case-insensitive match if the exact match fails
                                elif name.upper().strip() == specialty_name.upper().strip():
                                    specialty_id = id_val
                                    logger.info(f"Found case-insensitive match: {name} with ID {id_val}")
                                    break
                            
                            if specialty_id:
                                specialty_options.append({
                                    "label": specialty_name,
                                    "value": specialty_id
                                })
                                logger.info(f"Added WebSocket specialty button option: {specialty_name} (ID: {specialty_id})")
                            else:
                                logger.warning(f"Could not find ID for specialty in WebSocket: {specialty_name}")
                                # Try a more flexible match as a fallback
                                for name, id_val in agent.specialty_memory.items():
                                    if specialty_name.upper() in name.upper() or name.upper() in specialty_name.upper():
                                        specialty_id = id_val
                                        logger.info(f"Found partial match: {name} with ID {id_val}")
                                        specialty_options.append({
                                            "label": name,  # Use the name from memory for consistency
                                            "value": specialty_id
                                        })
                                        logger.info(f"Added WebSocket specialty button option via partial match: {name} (ID: {specialty_id})")
                                        break
                    
                    # Add buttons for specialty options if we have any valid IDs
                    if specialty_options:
                        # Log all specialty options for debugging
                        logger.info(f"Final specialty options for WebSocket response: {specialty_options}")
                        
                        suggested_actions = [
                            {
                                "type": "specialty_selection",
                                "options": specialty_options
                            }
                        ]
                        logger.info(f"Added {len(specialty_options)} specialty button options to WebSocket response")
                        
                        # Add suggested actions to the response content
                        response_content["suggested_actions"] = suggested_actions
                        logger.info("Added suggested_actions to WebSocket response")
                
                # Check if the response is a dictionary with specialty recommendations
                if isinstance(response, dict) and response.get("action_type") == "recommend_specialist":
                    filtered_suggestions = response.get("filtered_suggestions", [])
                    health_problem = response.get("health_problem", "")
                    
                    # Process specialty recommendations
                    if filtered_suggestions:
                        # Extract specialty options
                        specialty_options = []
                        for suggestion in filtered_suggestions:
                            specialty_id = suggestion.get("specialty_id")
                            specialty_name = suggestion.get("specialty")
                            
                            if specialty_id and specialty_name:
                                specialty_options.append({
                                    "label": specialty_name,
                                    "value": specialty_id
                                })
                                logger.info(f"Added WebSocket specialty button: {specialty_name} (ID: {specialty_id})")
                        
                        # Add buttons for specialty options
                        if specialty_options:
                            suggested_actions = [
                                {
                                    "type": "specialty_selection",
                                    "options": specialty_options
                                }
                            ]
                            logger.info(f"Added {len(specialty_options)} specialty buttons to WebSocket")
                            
                            # Format the response text
                            formatted_response = f"For {health_problem}, we recommend the following specialties:"
                            response_content["response"] = formatted_response
                            response_content["suggested_actions"] = suggested_actions
                
                # Debug log the final response content
                logger.info(f"WebSocket response: {json.dumps(response_content)[:200]}...")
                
                # Send response back to client
                await websocket.send_json(response_content)
                logger.info(f"Sent WebSocket response")
                
            except Exception as e:
                error_msg = f"Error processing message: {str(e)}"
                logger.error(error_msg)
                logger.error(traceback.format_exc())  # Add stack trace for better debugging
                await websocket.send_json({"error": error_msg})
    
    except WebSocketDisconnect:
        logger.info(f"WebSocket client disconnected (session: {current_session_id})")
    except Exception as e:
        logger.error(f"WebSocket error: {str(e)}")
        logger.error(traceback.format_exc())  # Add stack trace for better debugging
    finally:
        ws_handler.remove_client(websocket)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Set up Jinja2 templates
templates = Jinja2Templates(directory="templates")

# Serve chat interface
@app.get("/", response_class=HTMLResponse)
async def get_chat_interface(request: Request):
    """
    Serve the main chat interface
    """
    return templates.TemplateResponse("chat.html", {"request": request})

# WebSocket endpoint for logging
@app.websocket("/logs")
async def websocket_logs(websocket: WebSocket):
    """
    WebSocket endpoint for streaming log messages to the frontend
    """
    await websocket.accept()
    
    # Store reference to the WebSocket
    ws_handler.add_client(websocket)
    
    # Send existing logs to the client
    for log in ws_handler.logs:
        await websocket.send_text(json.dumps({"log": log}))
    
    try:
        # Keep the connection open
        while True:
            await asyncio.sleep(1)
    except WebSocketDisconnect:
        ws_handler.remove_client(websocket)
    except Exception as e:
        logger.error(f"WebSocket log error: {str(e)}")
        ws_handler.remove_client(websocket)

# Run the FastAPI app if executed as script
if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    ) 