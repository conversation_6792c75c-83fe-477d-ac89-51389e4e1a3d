"""
WebSocket handler for real-time logging and communication.
"""

import json
import logging
import asyncio
from typing import Set

class WebSocketLogHandler(logging.Handler):
    """Custom logging handler to capture logs for WebSocket broadcasting."""
    
    def __init__(self):
        super().__init__()
        self.logs = []
        self.clients: Set = set()
        self.loop = None  # Will store event loop reference when available
        
    def emit(self, record):
        """Emit a log record."""
        log_entry = self.format(record)
        self.logs.append(log_entry)
        
        # Store the log but don't try to broadcast it immediately
        # Broadcasting will happen when clients connect
        # This avoids the "no running event loop" error
        
    async def broadcast(self, message):
        """Broadcast a message to all connected WebSocket clients."""
        for client in list(self.clients):
            try:
                await client.send_text(json.dumps({"log": message}))
            except Exception:
                self.clients.remove(client)
    
    def add_client(self, websocket):
        """Add a WebSocket client to the broadcast list."""
        self.clients.add(websocket)
        
    def remove_client(self, websocket):
        """Remove a WebSocket client from the broadcast list."""
        if websocket in self.clients:
            self.clients.remove(websocket)
