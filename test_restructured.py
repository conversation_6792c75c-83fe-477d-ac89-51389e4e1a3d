#!/usr/bin/env python3
"""
Test script to verify the restructured code works correctly.
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all modules can be imported correctly."""
    try:
        print("Testing imports...")
        
        # Test config
        from config.settings import get_settings
        settings = get_settings()
        print(f"✓ Config loaded - Debug mode: {settings.debug_mode}")
        
        # Test database
        from database.db_connector import DBConnector
        print("✓ Database connector imported")
        
        # Test tools
        from tools.tools_manager import ToolsManager
        tools = ToolsManager(debug_mode=False)
        print("✓ Tools manager imported and initialized")
        
        # Test core agent
        from core.agent import ReActAgent
        agent = ReActAgent(debug_mode=False)
        print("✓ ReAct agent imported and initialized")
        
        # Test API components
        from api.models import ChatRequest, ChatResponse
        from api.websocket_handler import WebSocketLogHandler
        from api.chat_handler import ChatHandler
        
        chat_handler = ChatHandler(agent)
        print("✓ API components imported and initialized")
        
        print("\n✅ All imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ Import error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_functionality():
    """Test basic functionality of the restructured components."""
    try:
        print("\nTesting basic functionality...")
        
        # Test agent initialization
        from core.agent import ReActAgent
        agent = ReActAgent(debug_mode=False)
        
        # Test a simple greeting
        response = agent.chat("Hello", "test-session-123")
        print(f"✓ Agent chat response: {response[:100]}...")
        
        # Test tools manager
        from tools.tools_manager import ToolsManager
        tools = ToolsManager(debug_mode=False)
        
        # Test specialty recommendations (will fail without API but should handle gracefully)
        result = tools.recommend_specialist({"health_problem": "headache"})
        print(f"✓ Tools manager recommendation (expected to have error): {result.get('error', 'Success')}")
        
        print("\n✅ Basic functionality tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Functionality error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🧪 Testing Restructured SmartClinic ReAct Agent")
    print("=" * 50)
    
    success = True
    
    # Test imports
    if not test_imports():
        success = False
    
    # Test basic functionality
    if not test_basic_functionality():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed! The restructured code is working correctly.")
        print("\nYou can now run the main application with:")
        print("python main.py")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
