# SmartClinic ReAct Agent

A sophisticated AI-powered chatbot for hospital specialty recommendations and appointment management using the ReAct (Reason-Act-Observe) paradigm.

## 🚀 Features

- **🤖 AI-Powered Recommendations**: Intelligent specialist recommendations based on symptoms
- **🔄 Automatic Token Management**: Auto-refreshes API tokens every 24 hours
- **💾 Persistent Memory**: MongoDB-based conversation memory
- **🌐 Real-time Communication**: WebSocket support for live updates
- **📱 Modern Web Interface**: Clean, responsive chat interface
- **🔧 RESTful API**: Complete API for integration with other systems

## 📁 Project Structure

```
SmartClinic-ReAct-Agent/
├── 📁 api/                    # API layer components
├── 📁 config/                 # Configuration management
├── 📁 core/                   # Core business logic (ReAct Agent)
├── 📁 database/               # Database connectivity
├── 📁 tools/                  # External API tools
├── 📁 utils/                  # Utility functions (token management)
├── 📁 static/                 # Static web files
├── 📁 templates/              # HTML templates
├── 📄 main.py                 # Main FastAPI application
├── 📄 .env                    # Environment configuration
├── 📄 requirements.txt        # Python dependencies
└── 📄 test_restructured.py    # Test suite
```

## 🛠️ Installation & Setup

### Prerequisites
- Python 3.7+
- pip (Python package installer)

### Quick Start

```bash
# 1. Navigate to project directory
cd d:\SVN\dev_chatbot\carechatexperiment

# 2. Install dependencies
pip install -r requirements.txt

# 3. Test the installation
python test_restructured.py

# 4. Run the application
python main.py
```

### Environment Configuration

The application uses a `.env` file for configuration:

```env
# LLM Configuration
LLM_ENDPOINT=http://*********:1234/v1/chat/completions

# API Configuration
SPECIALTY_API_ENDPOINT=http://eserver/api/his/AppointmentsAPI/InitAll
SSO_ACTIVATION_ENDPOINT=http://eserver/api/visitmgmt/Accounts/ActivateSSO?Id=0x31343437B0A6F71A-2FC4-4D56-9709-D99A245DF2BB

# Database Configuration
MONGO_URI=mongodb://localhost:27017/

# Server Configuration
HOST=0.0.0.0
PORT=8000
DEBUG_MODE=true
RELOAD=true
```

## 🔐 Automatic Token Management

The application automatically:
- ✅ Fetches fresh tokens on startup
- ✅ Refreshes tokens every 30 minutes
- ✅ Updates the `.env` file with new tokens
- ✅ Handles token expiration gracefully
- ✅ Provides fallback tokens if SSO fails

### Token Endpoints

- **GET** `/api/token/status` - Check current token status
- **POST** `/api/token/refresh` - Manually refresh token

## 🌐 API Endpoints

### Chat API
- **POST** `/api/chat` - Send chat messages
- **GET** `/` - Web chat interface
- **WebSocket** `/logs` - Real-time logging

### Token Management
- **GET** `/api/token/status` - Token status
- **POST** `/api/token/refresh` - Manual token refresh

## 🧪 Testing

```bash
# Run comprehensive tests
python test_restructured.py

# Expected output:
# ✅ All imports successful!
# ✅ Basic functionality tests passed!
# 🎉 All tests passed!
```

## 🚀 Running the Application

```bash
# Start the server
python main.py

# Expected output:
# 🚀 Starting SmartClinic ReAct Agent...
# ✅ Automatic token refresh started
# 🎉 SmartClinic ReAct Agent started successfully!
# INFO: Uvicorn running on http://0.0.0.0:8000
```

### Access Points

- **Web Interface**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Token Status**: http://localhost:8000/api/token/status

## 🔧 Maintenance

### Clean Project
```bash
# Remove cache files and prepare for distribution
python cleanup.py
```

### Manual Token Refresh
```bash
# Via API
curl -X POST http://localhost:8000/api/token/refresh

# Via Python
python -c "from config.settings import get_settings; get_settings().refresh_token_if_needed()"
```

## 📊 Token Information Display

On startup, the application displays:
```
=== TOKEN INFORMATION ===
Token Status: ✅ Active
User: Sr Dr SAJEEWA
User ID: SDOC
Token Expires: 2024-01-22T10:30:00
Token Preview: eyJhbGciOiJIUzI1NiIs...
========================
```

## 🛡️ Security Features

- 🔐 Automatic token rotation (24-hour expiry)
- 🔒 Secure token storage in environment variables
- 🚫 Token preview (only first 20 characters shown in logs)
- ⚠️ Graceful fallback for token failures

## 🔄 Architecture Benefits

- **Modular Design**: Easy to maintain and extend
- **Automatic Token Management**: No manual intervention required
- **Persistent Memory**: Conversations saved in MongoDB
- **Real-time Updates**: WebSocket integration
- **Error Handling**: Graceful degradation
- **Testing**: Comprehensive test suite

## 📝 Notes

- Tokens are automatically refreshed every 24 hours
- The application works offline with fallback tokens
- MongoDB is optional (graceful degradation)
- All original functionality is preserved
- Clean, professional codebase ready for production

## 🎉 Success Indicators

✅ Test script passes all checks  
✅ Server starts without errors  
✅ Token fetched automatically  
✅ Web interface accessible  
✅ API responds correctly  
✅ Real-time logging works  

Your SmartClinic ReAct Agent is ready for production! 🚀
