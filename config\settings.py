"""
Configuration settings for the SmartClinic ReAct Agent.
Centralizes all environment variables and configuration.
"""

import os
import logging
import requests
import json
from datetime import datetime, timedelta
from dotenv import load_dotenv, set_key
from typing import Optional

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger("Settings")

class Settings:
    """Application settings and configuration."""

    def __init__(self):
        # LLM Configuration
        self.llm_endpoint = os.getenv("LLM_ENDPOINT", "http://*********:1234/v1/chat/completions")

        # API Configuration
        self.specialty_api_endpoint = os.getenv("SPECIALTY_API_ENDPOINT", "http://eserver/api/his/AppointmentsAPI/InitAll")
        self.sso_activation_endpoint = os.getenv("SSO_ACTIVATION_ENDPOINT", "http://eserver/api/visitmgmt/Accounts/ActivateSSO?Id=0x31343437B0A6F71A-2FC4-4D56-9709-D99A245DF2BB")

        # Database Configuration
        self.mongo_uri = os.getenv("MONGO_URI", "mongodb://localhost:27017/")

        # Server Configuration
        self.host = os.getenv("HOST", "0.0.0.0")
        self.port = int(os.getenv("PORT", "8000"))
        self.debug_mode = os.getenv("DEBUG_MODE", "true").lower() == "true"
        self.reload = os.getenv("RELOAD", "true").lower() == "true"

        # Token management
        self.specialty_api_token = None
        self.token_expiry = None
        self.user_info = {}

        # Initialize token
        self._initialize_token()

        logger.info(f"Settings initialized - Debug mode: {self.debug_mode}")

    def _initialize_token(self):
        """Initialize or refresh the API token."""
        try:
            # Try to get existing token from environment
            existing_token = os.getenv("SPECIALTY_API_TOKEN")

            if existing_token and existing_token.strip():
                # Check if token is still valid (basic check)
                self.specialty_api_token = existing_token
                logger.info("Using existing token from environment")
            else:
                # Fetch new token
                self._fetch_new_token()

        except Exception as e:
            logger.error(f"Error initializing token: {str(e)}")
            # Fallback to default token
            self._use_fallback_token()

    def _fetch_new_token(self):
        """Fetch a new token from the SSO activation endpoint."""
        try:
            logger.info("Fetching new token from SSO endpoint...")

            response = requests.get(self.sso_activation_endpoint, timeout=10)
            response.raise_for_status()

            token_data = response.json()

            if "Token" in token_data:
                self.specialty_api_token = token_data["Token"]

                # Store user information
                self.user_info = {
                    "token_type": token_data.get("TokenType", "Bearer"),
                    "user_id": token_data.get("UserId", ""),
                    "user_name": token_data.get("UserName", ""),
                    "expiry_date": token_data.get("ExpiryDate")
                }

                # Calculate expiry (24 hours from now if not provided)
                if token_data.get("ExpiryDate"):
                    self.token_expiry = token_data["ExpiryDate"]
                else:
                    # Default to 24 hours
                    self.token_expiry = (datetime.now() + timedelta(hours=24)).isoformat()

                # Update .env file with new token
                self._update_env_token(self.specialty_api_token)

                logger.info(f"✅ New token fetched successfully for user: {self.user_info.get('user_name', 'Unknown')}")
                logger.info(f"Token expires: {self.token_expiry}")

            else:
                logger.error("No token found in SSO response")
                self._use_fallback_token()

        except requests.exceptions.RequestException as e:
            logger.error(f"Network error fetching token: {str(e)}")
            self._use_fallback_token()
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON response from SSO endpoint: {str(e)}")
            self._use_fallback_token()
        except Exception as e:
            logger.error(f"Unexpected error fetching token: {str(e)}")
            self._use_fallback_token()

    def _use_fallback_token(self):
        """Use fallback token when automatic fetching fails."""
        logger.warning("Using fallback token - some features may not work properly")
        self.specialty_api_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1laWRlbnRpZmllciI6IjB4MzEzNDM0MzdCMEE2RjcxQS0yRkM0LTRENTYtOTcwOS1EOTlBMjQ1REYyQkIiLCJleHAiOjE3NTMxMDE2NDAsImlzcyI6Iml2aXZhY2FyZS5jb20iLCJhdWQiOiJpdml2YWNhcmUuY29tIn0.ChI3mME-fH6aDc8aeyQY1uqnhJC4Nhe05zF1Ck5R6Pk"
        self.token_expiry = (datetime.now() + timedelta(hours=1)).isoformat()  # Short expiry for fallback

    def _update_env_token(self, token: str):
        """Update the .env file with the new token."""
        try:
            env_file = ".env"
            if os.path.exists(env_file):
                set_key(env_file, "SPECIALTY_API_TOKEN", token)
                logger.info("Updated .env file with new token")
        except Exception as e:
            logger.error(f"Error updating .env file: {str(e)}")

    def refresh_token_if_needed(self):
        """Check if token needs refresh and refresh if necessary."""
        try:
            if self.token_expiry:
                expiry_time = datetime.fromisoformat(self.token_expiry.replace('Z', '+00:00'))
                current_time = datetime.now()

                # Refresh if token expires in less than 1 hour
                if (expiry_time - current_time).total_seconds() < 3600:
                    logger.info("Token expiring soon, refreshing...")
                    self._fetch_new_token()
                    return True
            return False
        except Exception as e:
            logger.error(f"Error checking token expiry: {str(e)}")
            return False

    def get_token_info(self):
        """Get current token information."""
        return {
            "has_token": bool(self.specialty_api_token),
            "user_info": self.user_info,
            "token_expiry": self.token_expiry,
            "token_preview": f"{self.specialty_api_token[:20]}..." if self.specialty_api_token else None
        }

# Global settings instance
_settings = None

def get_settings() -> Settings:
    """Get the global settings instance."""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings
