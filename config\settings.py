"""
Configuration settings for the SmartClinic ReAct Agent.
Centralizes all environment variables and configuration.
"""

import os
import logging
from dotenv import load_dotenv
from typing import Optional

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger("Settings")

class Settings:
    """Application settings and configuration."""
    
    def __init__(self):
        # LLM Configuration
        self.llm_endpoint = os.getenv("LLM_ENDPOINT", "http://************:1234/v1/chat/completions")
        
        # API Configuration
        self.specialty_api_endpoint = os.getenv("SPECIALTY_API_ENDPOINT", "http://eserver/api/his/AppointmentsAPI/InitAll")
        self.specialty_api_token = os.getenv("SPECIALTY_API_TOKEN")
        
        # Database Configuration
        self.mongo_uri = os.getenv("MONGO_URI", "mongodb://localhost:27017/")
        
        # Server Configuration
        self.host = os.getenv("HOST", "0.0.0.0")
        self.port = int(os.getenv("PORT", "8000"))
        self.debug_mode = os.getenv("DEBUG_MODE", "true").lower() == "true"
        self.reload = os.getenv("RELOAD", "true").lower() == "true"
        
        # Default token if not provided
        if not self.specialty_api_token:
            logger.warning("SPECIALTY_API_TOKEN not found in environment variables. Using default token.")
            self.specialty_api_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1laWRlbnRpZmllciI6IjB4MzEzNDM0MzdCMEE2RjcxQS0yRkM0LTRENTYtOTcwOS1EOTlBMjQ1REYyQkIiLCJleHAiOjE3NTMxMDE2NDAsImlzcyI6Iml2aXZhY2FyZS5jb20iLCJhdWQiOiJpdml2YWNhcmUuY29tIn0.ChI3mME-fH6aDc8aeyQY1uqnhJC4Nhe05zF1Ck5R6Pk"
        
        logger.info(f"Settings initialized - Debug mode: {self.debug_mode}")

# Global settings instance
_settings = None

def get_settings() -> Settings:
    """Get the global settings instance."""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings
