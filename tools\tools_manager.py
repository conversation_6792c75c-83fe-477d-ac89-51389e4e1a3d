"""
Tools manager for hospital chatbot using various APIs.
Contains tools for doctor specialty lookup and appointment management.
"""

import json
import requests
import os
import logging
import pendulum
from typing import Dict, Any, Optional

logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger("ToolsManager")

class ToolsManager:
    """
    Tools manager for hospital chatbot using various APIs.
    Contains tools for doctor specialty lookup and appointment management.
    """
    
    def __init__(self, 
                 specialty_api_endpoint: str = "http://eserver/api/his/AppointmentsAPI/InitAll",
                 specialty_api_token: Optional[str] = None,
                 debug_mode: bool = True):
        """
        Initialize the Tools Manager.
        
        Args:
            specialty_api_endpoint: Endpoint for the doctor specialty API
            specialty_api_token: Bearer token for API authentication
            debug_mode: Whether to show detailed debugging information
        """
        self.specialty_api_endpoint = specialty_api_endpoint
        self.specialty_api_token = specialty_api_token or os.getenv("SPECIALTY_API_TOKEN")
        self.debug_mode = debug_mode
        
        # Default headers with token
        self.headers = {
            "accept": "*/*",
            "authorization": f"Bearer {self.specialty_api_token}"
        }
        
        # Initialize specialty name cache
        self.specialty_name_cache = {}
        
        logger.info("Tools Manager initialized with debug_mode=%s", debug_mode)
    
    def _format_time_to_iso(self, time_str: str) -> str:
        """
        Helper function to format time string to ISO 8601 format with UTC indicator.
        
        Args:
            time_str: Time string in format like "10:30" or "10:30:00"
            
        Returns:
            ISO formatted time string with UTC indicator (e.g., "10:30:00Z")
        """
        if not time_str:
            return None
            
        try:
            # Handle different time formats using Pendulum
            if len(time_str) == 5:  # "10:30"
                time_str += ":00"
            elif len(time_str) == 8:  # "10:30:00"
                pass
            else:
                # If it's already in ISO format or other format, return as is
                return time_str
                
            # Add UTC indicator
            return f"{time_str}Z"
            
        except Exception as e:
            logger.error(f"Error formatting time {time_str}: {str(e)}")
            return time_str  # Return original if formatting fails
    
    def get_doctor_specialties(self, parameters: Dict[str, str]) -> Dict[str, Any]:
        """
        Get doctor specialties from the API.
        
        Args:
            parameters: Parameters for the API call (e.g., {"query": "cardio"})
            
        Returns:
            Doctor specialty information
        """
        # Check if we have the API token
        if not self.specialty_api_token:
            logger.error("Specialty API token not provided")
            raise ValueError("Specialty API token not provided")
        
        try:
            logger.info(f"Making API request to {self.specialty_api_endpoint}")
            response = requests.get(
                self.specialty_api_endpoint, 
                headers=self.headers
            )
            response.raise_for_status()
            
            # For demonstration/debug, log the raw response
            if self.debug_mode:
                logger.info(f"Raw API response: {json.dumps(response.json(), indent=2)[:500]}...")
            
            # Get all specialties from the response
            all_specialties = response.json().get("Codes", {}).get("SPECIALITY", [])
            logger.info(f"Retrieved {len(all_specialties)} specialties from API")
            
            # If a query parameter is provided, filter the results
            query = parameters.get("query", "").upper()
            
            # Check if this is explicitly marked as a full list request in parameters
            is_full_list = parameters.get("is_full_list", False)
            
            # Also check if the query suggests it's a request for the full list
            full_list_terms = ["FULL", "ALL", "COMPLETE", "YES", "YEAH", "SURE", "LIST", "SHOW", "MORE"]
            is_full_list_request = any(term in query.split() for term in full_list_terms)
            
            # Check if this is a general query about available specialties
            general_query_terms = ["AVAILABLE", "LIST", "ALL", "WHAT", "WHICH", "HAVE", "OFFER", "CONTINUE", "NEXT"]
            is_general_query = any(term in query for term in general_query_terms)
            
            # For full list requests or general queries, return all specialties
            if is_full_list or is_full_list_request or is_general_query:
                logger.info("Returning all specialties (full list request or general query)")
                return {"specialties": all_specialties, "is_full_list": True, "action_type": "get_doctor_specialties"}
            
            # For specific specialty queries
            if query:
                # Extract query terms for more flexible matching
                query_terms = query.split()
                
                # Filter out common words that aren't helpful for matching
                stop_words = ["WHAT", "WHICH", "ARE", "IS", "THE", "DO", "DOES", "YOU", "HAVE", "AVAILABLE", "THERE", "ANY", "FOR", "A", "AN", "IN", "AT", "BY", "WITH", "ABOUT", "PLEASE", "CAN", "COULD", "WOULD"]
                query_terms = [term for term in query_terms if term not in stop_words]
                
                logger.info(f"Filtering specialties by query terms: {query_terms}")
                filtered_specialties = []
                
                # Check each specialty against each query term
                for specialty in all_specialties:
                    desc = specialty.get("DESCRIPTION", "").upper()
                    
                    # Match if any term is contained in the description
                    if any(term in desc for term in query_terms):
                        filtered_specialties.append(specialty)
                
                logger.info(f"Found {len(filtered_specialties)} matching specialties")
                return {"specialties": filtered_specialties, "action_type": "get_doctor_specialties"}
            else:
                # For empty queries, return all specialties
                logger.info("Returning all specialties (no specific terms)")
                return {"specialties": all_specialties, "action_type": "get_doctor_specialties"}
                
        except Exception as e:
            logger.error(f"Error calling specialty API: {str(e)}")
            return {"error": str(e), "action_type": "get_doctor_specialties"}
    
    def activate_sso(self, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Activate SSO for a user.
        
        Args:
            parameters: Optional parameters (not used in this function)
            
        Returns:
            SSO activation result
        """
        try:
            url = "http://eserver/api/visitmgmt/Accounts/ActivateSSO?Id=$2a$06$209Th1Z/ZBraqhPa2PIQDeM/7T65Y6Y6MRS6YjefwVomvFAuMwYtG"
            res = requests.get(url, headers={"accept": "*/*"})
            return res.json()
        except Exception as e:
            logger.error(f"Error activating SSO: {str(e)}")
            return {"error": str(e)}
    
    def search_by_id_number(self, parameters: Dict[str, str]) -> Dict[str, Any]:
        """
        Search for a patient by ID number.
        
        Args:
            parameters: Parameters for the search (e.g., {"id_number": "DD15021998"})
            
        Returns:
            Search results
        """
        try:
            id_number = parameters.get("id_number", "DD15021998")  # Default to example ID if not provided
            url = f"http://eserver/api/clinicaldocs/Codes/SearchText?CodeName=CHECKIDNO&text={id_number}"
            res = requests.get(url, headers=self.headers)
            return res.json()
        except Exception as e:
            logger.error(f"Error searching by ID number: {str(e)}")
            return {"error": str(e)}
    
    def get_today_appointments(self, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Get today's appointments.
        
        Args:
            parameters: Optional parameters (not used in this function)
            
        Returns:
            Today's appointments
        """
        try:
            url = "http://eserver/api/clinicaldocs/VisitDocs/GetRecordset?VisitId=3598&QueryName=GET_TODAYAPPTS"
            res = requests.get(url, headers=self.headers)
            return res.json()
        except Exception as e:
            logger.error(f"Error getting today's appointments: {str(e)}")
            return {"error": str(e)}
    
    def get_ongoing_visits(self, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Get ongoing visits.
        
        Args:
            parameters: Optional parameters (not used in this function)
            
        Returns:
            Ongoing visits
        """
        try:
            url = "http://eserver/api/clinicaldocs/VisitDocs/GetRecordset?VisitId=3598&QueryName=GET_ONGOINGVISITS"
            res = requests.get(url, headers=self.headers)
            return res.json()
        except Exception as e:
            logger.error(f"Error getting ongoing visits: {str(e)}")
            return {"error": str(e)}
    
    def init_appointments(self, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Initialize appointments API.
        
        Args:
            parameters: Optional parameters (not used in this function)
            
        Returns:
            Initialization data
        """
        try:
            url = "http://eserver/api/his/AppointmentsAPI/InitAll"
            res = requests.get(url, headers=self.headers)
            return res.json()
        except Exception as e:
            logger.error(f"Error initializing appointments: {str(e)}")
            return {"error": str(e)}

    def recommend_specialist(self, parameters: Dict[str, str]) -> Dict[str, Any]:
        """
        Recommend an appropriate medical specialist based on a health issue or symptom.

        Args:
            parameters: Parameters for the recommendation
                - health_problem: Description of the health issue or symptom

        Returns:
            Recommendation for the appropriate specialist
        """
        try:
            # Get the health problem from parameters
            health_problem = parameters.get("health_problem", "")
            if not health_problem or not health_problem.strip():
                logger.error("No health problem provided or it's empty")
                return {"error": "No health problem provided", "action_type": "recommend_specialist"}

            logger.info(f"Recommending specialist for: {health_problem}")

            # First, get all specialties from the API
            specialties_response = self.get_doctor_specialties({})

            # Check if we got a valid response
            if "error" in specialties_response:
                logger.error(f"Error getting specialties: {specialties_response['error']}")
                return {"error": specialties_response["error"], "action_type": "recommend_specialist"}

            # Get the list of specialties
            all_specialties = specialties_response.get("specialties", [])

            # Simple rule-based recommendation for now
            recommendations = self._get_specialty_recommendations(health_problem, all_specialties)

            return {
                "action_type": "recommend_specialist",
                "filtered_suggestions": recommendations,
                "health_problem": health_problem
            }

        except Exception as e:
            logger.error(f"Error recommending specialist: {str(e)}")
            return {"error": f"Error recommending specialist: {str(e)}", "action_type": "recommend_specialist"}

    def _get_specialty_recommendations(self, health_problem: str, all_specialties: list) -> list:
        """
        Get specialty recommendations based on health problem.
        This is a simplified rule-based approach.
        """
        problem_lower = health_problem.lower()
        recommendations = []

        # Create a mapping of specialties
        specialty_mapping = {}
        for specialty in all_specialties:
            desc = specialty.get("DESCRIPTION", "")
            identifier = specialty.get("IDENTIFIER", "")
            if desc and identifier:
                specialty_mapping[desc.lower()] = {
                    "original": desc,
                    "id": str(identifier)
                }

        # Simple keyword-based matching
        if any(word in problem_lower for word in ["chest pain", "heart", "cardiac", "palpitation"]):
            if "cardiology" in specialty_mapping:
                recommendations.append({
                    "specialty": specialty_mapping["cardiology"]["original"],
                    "specialty_id": specialty_mapping["cardiology"]["id"],
                    "reason": "Chest pain and heart-related symptoms require cardiac evaluation"
                })

        if any(word in problem_lower for word in ["headache", "migraine", "seizure", "stroke", "neurological"]):
            if "neurology" in specialty_mapping:
                recommendations.append({
                    "specialty": specialty_mapping["neurology"]["original"],
                    "specialty_id": specialty_mapping["neurology"]["id"],
                    "reason": "Neurological symptoms require specialist evaluation"
                })

        if any(word in problem_lower for word in ["bone", "joint", "fracture", "orthopedic", "back pain"]):
            # Look for orthopedic specialties
            for name, data in specialty_mapping.items():
                if "orthopaedic" in name or "orthopedic" in name:
                    recommendations.append({
                        "specialty": data["original"],
                        "specialty_id": data["id"],
                        "reason": "Bone and joint problems require orthopedic evaluation"
                    })
                    break

        # If no specific matches, recommend general medicine
        if not recommendations:
            for name, data in specialty_mapping.items():
                if "general medicine" in name:
                    recommendations.append({
                        "specialty": data["original"],
                        "specialty_id": data["id"],
                        "reason": "General medicine can provide initial assessment and referral if needed"
                    })
                    break

        return recommendations

    def get_user_dataset(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get user dataset for appointments.

        Args:
            parameters: Parameters for the dataset query
                - date_from: Start date (default: current date)
                - date_to: End date (default: current date)
                - resource_type: Resource type (default: 1)
                - specialty_id: Specialty ID (required)
                - query_name: Query name (default: APPTFINDRESO_AI)

        Returns:
            User dataset with doctor availability information
        """
        try:
            # Get the query name, defaulting to APPTFINDRESO_AI
            query_name = parameters.get("query_name", "APPTFINDRESO_AI")

            # Extract parameters with defaults
            current_date = pendulum.now("Asia/Singapore").format("YYYY-MM-DD")

            date_from = parameters.get("date_from", current_date)
            date_to = parameters.get("date_to", current_date)
            resource_type = parameters.get("resource_type", "1")

            # Verify that specialty_id is provided
            specialty_id = parameters.get("specialty_id")
            if not specialty_id:
                logger.error("No specialty_id provided for get_user_dataset")
                return {
                    "error": "No specialty_id provided",
                    "action_type": "get_user_dataset"
                }

            logger.info(f"Getting doctors for specialty ID: {specialty_id} from {date_from} to {date_to}")

            # Construct the request body
            body = {
                "RESOURCETYPE": resource_type,
                "SPECIALITYID": specialty_id,
                "RESOURCEID": parameters.get("resource_id"),
                "CLINICID": parameters.get("clinic_id"),
                "FROMDATE": date_from,
                "TODATE": date_to,
                "FROM_TIME": parameters.get("from_time"),
                "TO_TIME": parameters.get("to_time")
            }

            # Define the API endpoint and headers
            url = f"http://eserver/api/his/AppointmentsAPI/GetUserDataset?QueryName={query_name}"
            headers = {
                "accept": "text/plain",
                "Authorization": f"Bearer {self.specialty_api_token}",
                "Content-Type": "application/json"
            }

            logger.info(f"Making API request to {url}")

            # Make the API call
            response = requests.post(url, headers=headers, json=body)
            response.raise_for_status()

            # Parse the response
            api_result = response.json()
            logger.info(f"API response received successfully")

            # Process the API result to extract doctor information
            available_doctors = []

            for session_data in api_result:
                resource_id = session_data.get("RESOURCEID")
                session_id = session_data.get("SESSIONID")

                # Format the doctor name properly
                if session_data.get('TITLE') and session_data.get('NAME'):
                    doctor_name = f"{session_data.get('TITLE')} {session_data.get('NAME')}"
                else:
                    doctor_name = session_data.get('NAME', 'Unknown Doctor')

                # Get session details
                start_time_str = session_data.get("STARTTIME", "")
                available_slots = session_data.get("AVAILABLESLOTS", 0)
                session_date = session_data.get("SESSIONDATE", "")

                available_doctors.append({
                    "name": doctor_name,
                    "start_time_display": start_time_str,
                    "available_slots": available_slots,
                    "resource_id": resource_id,
                    "session_id": session_id,
                    "session_date": session_date
                })

            return {
                "action_type": "get_user_dataset",
                "available_doctors": available_doctors,
                "doctor_count": len(available_doctors)
            }

        except Exception as e:
            logger.error(f"Error getting user dataset: {str(e)}")
            return {"error": f"Error getting user dataset: {str(e)}", "action_type": "get_user_dataset"}

    def get_session_slots(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get session slots for a specific resource and date.

        Args:
            parameters: Parameters containing resource_id, session_date, session_id

        Returns:
            Session slots information
        """
        try:
            # This is a placeholder implementation
            # In a real system, this would call the actual API
            return {
                "slots": [],
                "message": "Session slots functionality not fully implemented"
            }
        except Exception as e:
            logger.error(f"Error getting session slots: {str(e)}")
            return {"error": str(e)}

    def create_walkin(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a walk-in appointment.

        Args:
            parameters: Parameters for creating walk-in appointment

        Returns:
            Walk-in creation result
        """
        try:
            # This is a placeholder implementation
            # In a real system, this would call the actual API
            return {
                "message": "Walk-in creation functionality not fully implemented"
            }
        except Exception as e:
            logger.error(f"Error creating walk-in: {str(e)}")
            return {"error": str(e)}

    def get_appointment_number(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get appointment number details.

        Args:
            parameters: Parameters containing visit_id

        Returns:
            Appointment number details
        """
        try:
            # This is a placeholder implementation
            return {
                "message": "Get appointment number functionality not fully implemented"
            }
        except Exception as e:
            logger.error(f"Error getting appointment number: {str(e)}")
            return {"error": str(e)}

    def create_visit(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a visit.

        Args:
            parameters: Parameters for creating visit

        Returns:
            Visit creation result
        """
        try:
            # This is a placeholder implementation
            return {
                "message": "Create visit functionality not fully implemented"
            }
        except Exception as e:
            logger.error(f"Error creating visit: {str(e)}")
            return {"error": str(e)}

    def get_patient_journey(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get patient journey information.

        Args:
            parameters: Parameters containing appointment_id

        Returns:
            Patient journey information
        """
        try:
            # This is a placeholder implementation
            return {
                "message": "Get patient journey functionality not fully implemented"
            }
        except Exception as e:
            logger.error(f"Error getting patient journey: {str(e)}")
            return {"error": str(e)}

    def get_appointment_followup(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get appointment follow-up information.

        Args:
            parameters: Parameters for follow-up query

        Returns:
            Follow-up information
        """
        try:
            # This is a placeholder implementation
            return {
                "message": "Get appointment follow-up functionality not fully implemented"
            }
        except Exception as e:
            logger.error(f"Error getting appointment follow-up: {str(e)}")
            return {"error": str(e)}
