import json
import requests
import os
import logging
import pendulum
from typing import Dict, Any, Optional
logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger("Tools")

class Tools:
    """
    Tools for hospital chatbot using various APIs.
    Contains tools for doctor specialty lookup and appointment management.
    """
    
    def __init__(self, 
                 specialty_api_endpoint: str = "http://eserver/api/his/AppointmentsAPI/InitAll",
                 specialty_api_token: Optional[str] = None,
                 debug_mode: bool = True):
        """
        Initialize the Tools.
        
        Args:
            specialty_api_endpoint: Endpoint for the doctor specialty API
            specialty_api_token: Bearer token for API authentication
            debug_mode: Whether to show detailed debugging information
        """
        self.specialty_api_endpoint = specialty_api_endpoint
        self.specialty_api_token = specialty_api_token or os.getenv("SPECIALTY_API_TOKEN")
        self.debug_mode = debug_mode
        
        # Default headers with token
        self.headers = {
            "accept": "*/*",
            "authorization": f"Bearer {self.specialty_api_token}"
        }
        
        # Initialize specialty name cache
        self.specialty_name_cache = {}
        
        logger.info("Tools initialized with debug_mode=%s", debug_mode)
    
    def _format_time_to_iso(self, time_str: str) -> str:
        """
        Helper function to format time string to ISO 8601 format with UTC indicator.
        
        Args:
            time_str: Time string in format like "10:30" or "10:30:00"
            
        Returns:
            ISO formatted time string with UTC indicator (e.g., "10:30:00Z")
        """
        if not time_str:
            return None
            
        try:
            # Handle different time formats using Pendulum
            if len(time_str) == 5:  # "10:30"
                time_str += ":00"
            elif len(time_str) == 8:  # "10:30:00"
                pass
            else:
                # If it's already in ISO format or other format, return as is
                return time_str
                
            # Add UTC indicator
            return f"{time_str}Z"
            
        except Exception as e:
            logger.error(f"Error formatting time {time_str}: {str(e)}")
            return time_str  # Return original if formatting fails
    
    def recommend_specialist(self, parameters: Dict[str, str]) -> Dict[str, Any]:
        """
        Recommend an appropriate medical specialist based on a health issue or symptom.
        
        Args:
            parameters: Parameters for the recommendation
                - health_problem: Description of the health issue or symptom
            
        Returns:
            Recommendation for the appropriate specialist
        """
        try:
            # Get the health problem from parameters
            health_problem = parameters.get("health_problem", "")
            if not health_problem or not health_problem.strip():
                logger.error("No health problem provided or it's empty")
                return {"error": "No health problem provided", "action_type": "recommend_specialist"}
                
            # Log the health problem for debugging
            logger.info(f"Processing health problem for specialist recommendation: '{health_problem}'")
            
            logger.info(f"Recommending specialist for: {health_problem}")
            
            # Check for emergency symptoms first
            emergency_detection = self._detect_emergency_symptoms(health_problem)
            logger.info(f"Emergency detection result: {emergency_detection}")
            
            # First, get all specialties from the API
            specialties_response = self.get_doctor_specialties({})
            
            # Check if we got a valid response
            if "error" in specialties_response:
                logger.error(f"Error getting specialties: {specialties_response['error']}")
                return {"error": specialties_response["error"], "action_type": "recommend_specialist"}
            
            # Get the list of specialties
            all_specialties = specialties_response.get("specialties", [])
            
            # Get all specialty names for better context
            available_specialty_names = [s.get("DESCRIPTION", "") for s in all_specialties]
            specialty_names = available_specialty_names  # Keep for backward compatibility
            
            # Format available specialties as a comma-separated string
            available_specialties = ", ".join(specialty_names)
            
            # Use the LLM endpoint to recommend a specialist based on the health problem
            llm_endpoint = os.getenv("LLM_ENDPOINT", "http://************:1234/v1/chat/completions")
            headers = {
                "Content-Type": "application/json"
            }
            
            # Comprehensive symptom-to-specialty mapping based on medical data
            specialty_symptom_guide = """
            COMPREHENSIVE MEDICAL SPECIALTY GUIDE:

            1. Accident & Emergency: Chest pain, seizures, trauma, bleeding, burns, shortness of breath, stroke, fainting, overdose, acute allergic reaction

            2. OPS (Outpatient Services): Cough, fever, fatigue, sore throat, headache, mild infections, nausea, diarrhea, runny nose, general check-up

            3. Gastrointestinal Surgery: Hernia, gallstones, appendicitis, GI tumors, bowel obstruction, severe abdominal pain, peritonitis, rectal bleeding, anal fissures

            4. Breast Services: Breast lump, nipple discharge, breast pain, swelling, redness, infection, skin dimpling, abnormal mammogram, breast asymmetry

            5. TOZ (Tumor/Oncology): Tumor evaluation, biopsy referral, suspicious mass, lymph node swelling, cancer screening, unexplained weight loss, fatigue

            6. Cardiothoracic Surgery: Chest pain, breathlessness, valve disorders, coronary artery disease, lung nodules, pleural effusion, collapsed lung, thoracic tumors

            7. Dentistry: Toothache, cavities, gum bleeding, jaw pain, mouth ulcers, sensitivity, bad breath, loose teeth, swelling

            8. ENT (Ear, Nose & Throat): Sinusitis, ear pain, sore throat, nasal congestion, nosebleeds, tinnitus, hoarseness, tonsillitis, vertigo, ear discharge

            9. Eye/Ophthalmology: Blurry vision, red eyes, eye pain, dryness, floaters, sensitivity to light, glaucoma, cataracts, itching, discharge

            10. Geriatric Medicine: Memory loss, confusion, falls, incontinence, joint pain, frailty, sleep disturbance, polypharmacy, depression, loss of appetite

            11. General Medicine: Fever, fatigue, cough, diabetes,bp, infections, anemia, body aches, sore throat, dizziness

            12. Mixed - Med, Ortho, Surg: Joint pain, injury, fatigue, diabetes, abdominal pain, fever, swelling, back pain, general surgery consult

            13. Orthopaedic Surgery: Fractures, arthritis, joint dislocation, torn ligaments, back pain, carpal tunnel, hip pain, scoliosis, sports injuries

            14. Other: Rare diseases, multi-system symptoms, undiagnosed issues, complex cases, orphan conditions

            15. Podiatry: Heel pain, flat feet, bunions, calluses, corns, fungal toenails, diabetic foot, foot ulcers, plantar fasciitis

            16. Psychological Medicine: Depression, anxiety, insomnia, phobias, bipolar disorder, OCD, PTSD, panic attacks, hallucinations, mood swings

            17. Rehabilitation: Post-stroke recovery, mobility issues, muscle weakness, injury rehab, post-op recovery, gait training, chronic pain rehab

            18. General Surgery: Appendicitis, hernias, gallstones, lumps, lipomas, abscesses, hemorrhoids, surgical infections

            19. Radiology/X-ray: Fractures, tumors, pneumonia, kidney stones, lung lesions, stroke imaging, joint dislocation, abdominal masses

            20. Cardiology: Chest pain, palpitations, fainting, high BP, heart failure, shortness of breath, swelling, irregular heartbeat, angina

            21. Urology: Burning urination, blood in urine, frequent urination, kidney stones, prostate issues, urinary retention, incontinence

            22. Geriatric Medicine: Memory loss, confusion, falls, incontinence, joint pain, frailty, sleep disturbance, polypharmacy, depression, loss of appetite

            23. Orthopaedic Medicine: Arthritis, joint pain, sprains, stiffness, musculoskeletal pain, bone deformities, osteoporosis

            24. General Medicine: Fever, fatigue, cough, diabetes, high BP, infections, anemia, body aches, sore throat, dizziness

            25. Surgery: Appendicitis, hernias, gallstones, lumps, lipomas, abscesses, hemorrhoids, surgical infections

            26. Rehabilitation Medicine: Post-stroke recovery, mobility issues, muscle weakness, injury rehab, post-op recovery, gait training, chronic pain rehab

            27. Anaesthesia: Pre-surgery assessment, pain management, spinal blocks, nerve blocks, anesthesia allergy history, sedation monitoring

            28. X-ray (Old): Basic imaging for bones, lungs, joints

            29. Anaesthesia: Pre-surgery assessment, pain management, spinal blocks, nerve blocks, anesthesia allergy history, sedation monitoring

            30. Dietetic Consultation: Obesity, undernutrition, diabetes diet, cholesterol control, pregnancy nutrition, food allergies, IBS-related diet

            31. Neurology: Seizures, stroke, migraine, numbness, dizziness, tremors, memory issues, facial droop, neuropathy, vision loss

            32. Physiotherapy: Muscle stiffness, post-surgery rehab, neck pain, back pain, sports injuries, post-fracture recovery, joint mobility

            33. Occupational Therapy: Difficulty dressing, post-stroke daily tasks, hand rehab, motor skill deficits, cognitive rehab

            34. Speech Therapy: Speech delay, stammering, aphasia, difficulty swallowing, slurred speech, voice issues, lisps, post-stroke speech

            35. Day Hospital: Chemotherapy, infusion therapies, minor procedures, chronic disease management, wound care

            36. Podiatry: Heel pain, flat feet, bunions, calluses, corns, fungal toenails, diabetic foot, foot ulcers, plantar fasciitis

            37. Neurosurgery: Brain tumors, spine disorders, hydrocephalus, herniated discs, chronic headaches, spinal cord compression

            38. Neurology: Seizures, stroke, migraine, numbness, dizziness, tremors, memory issues, facial droop, neuropathy, vision loss

            39. Neurosurgery: Brain tumors, spine disorders, hydrocephalus, herniated discs, chronic headaches, spinal cord compression

            40. Obstetrics & Gynaecology: Pregnancy, menstrual issues, infertility, menopause, PCOS, pelvic pain, fibroids, discharge, prenatal care

            41. Oral & Maxillofacial Surgery: Jaw fractures, cysts, impacted teeth, tumors, TMJ disorders, facial trauma

            42. Gastroenterology: Bloating, constipation, acid reflux, nausea, diarrhea, IBS, Crohn's disease, liver problems, hepatitis

            43. MSS (Unclear): Specialty requires clarification

            44. Dermatology: Acne, eczema, rashes, skin infections, hair loss, fungal infections, psoriasis, warts, pigmentation

            45. FGD (Unclear): Specialty requires clarification

            46. SFSDSFSD (Placeholder): Specialty requires clarification

            47. Description from Tara (Placeholder): Specialty requires clarification

            48. CMS (Likely non-clinical): Specialty requires clarification

            49. Toxicology: Drug overdose, poisoning, alcohol toxicity, industrial exposure, chemical burns, heavy metal toxicity

            50. Endocrinology: Diabetes, thyroid disorders, obesity, PCOS, adrenal issues, hormone imbalance, fatigue, hair thinning

            51. Respiratory Medicine: Asthma, COPD, wheezing, chronic cough, pneumonia, shortness of breath, lung infections, TB

            52. Renal (Nephrology): Edema, high creatinine, kidney stones, UTI, frequent urination, dialysis, high BP

            53. Neonatology: Jaundice in newborns, low birth weight, breathing issues, infections, feeding problems, premature care

            54. Geriatric Medicine: Memory loss, confusion, falls, incontinence, joint pain, frailty, sleep disturbance, polypharmacy, depression, loss of appetite

            55. Sports Medicine: Sprains, ligament tears, joint pain, performance issues, overuse injuries, rehab planning

            56. CTR (Needs clarification): Specialty requires clarification

            57. TC (Possibly Tumor Clinic): Specialty requires clarification

            58. RHI: Stroke rehab, physical disability recovery, long-term mobility issues, brain injury rehab

            59. RHP: Pain management, neuromuscular rehab, walking difficulty, post-accident recovery

            60. HS (Health Screening): BP, cholesterol, diabetes, BMI check, preventive check-ups, vision, hearing

            61. HA TEST: Hearing test, tinnitus evaluation, hearing loss, ear sensitivity

            62. Vascular Surgery: Varicose veins, leg pain when walking, poor circulation, deep vein thrombosis, ulcers

            63. CV (Same as Cardiology): Chest pain, palpitations, fainting, high BP, heart failure, shortness of breath, swelling, irregular heartbeat, angina

            64. PHC (Primary Health Care): Flu, cold, headache, chronic care, diabetes monitoring, general check-up, vaccination
            """
            
            # ROSE Prompt for the LLM with comprehensive medical mapping
            system_prompt = f"""
            Role: You are an expert medical advisor with comprehensive knowledge of medical specialties and their appropriate use cases. You have access to detailed symptom-to-specialty mappings based on clinical practice guidelines.
            
            Objective: Provide the most accurate medical specialty recommendations based on the user's symptoms or health problems. Your recommendations must be medically sound and based on the comprehensive specialty guide provided.
            
            Style: Be precise, medically accurate, and prioritize patient safety. Always consider emergency situations.
            
            Expected Output: Respond ONLY with a valid JSON object containing:
            1. "suggestions": An array of 2-3 most appropriate specialties, each with:
               - "specialty": The exact name of the medical specialty from available options
               - "reason": A brief, medically accurate reason for the recommendation
            2. "emergency_advice": Include if symptoms suggest potential emergency (chest pain, severe bleeding, difficulty breathing, stroke symptoms, etc.)

            {specialty_symptom_guide}

            Available specialties at this hospital:
            {', '.join(available_specialty_names)}

            CRITICAL GUIDELINES:
            - Use ONLY the exact specialty names from the available list
            - Prioritize emergency conditions - if symptoms are potentially serious, ALWAYS include "Accident & Emergency" as one of the specialties
            - Consider symptom severity and duration
            - For vague symptoms, recommend General Medicine first
            - IMPORTANT: For most symptoms, recommend 2-3 relevant specialties to give patients options
            - Always include emergency advice for potentially serious symptoms
            - For common symptoms like fever, pain, etc., always include General Medicine as one option

            EMERGENCY SYMPTOMS - Always include emergency advice for these:
            - Chest pain (any type)
            - Severe headache or sudden headache
            - Shortness of breath or difficulty breathing
            - Sudden weakness, numbness, facial droop, slurred speech
            - Severe abdominal pain
            - Uncontrolled bleeding
            - Loss of consciousness or fainting
            - Seizures
            - High fever with stiff neck, rash, or confusion
            - Severe allergic reactions
            - Sudden vision loss or changes
            - Severe burns
            - Poisoning or overdose

            Example responses:

            For "chest pain":
            {{
              "suggestions": [
                {{"specialty": "CARDIOLOGY", "reason": "Chest pain requires immediate cardiac evaluation to rule out heart attack or other cardiac conditions"}},
                {{"specialty": "Accident & Emergency", "reason": "Acute chest pain may require emergency assessment and treatment"}},
                {{"specialty": "GENERAL MEDICINE", "reason": "For initial assessment and coordination of care if multiple systems are involved"}}
              ],
              "emergency_advice": "Severe chest pain, especially with shortness of breath, sweating, nausea, or arm pain, requires immediate emergency care - call emergency services"
            }}

            For "stomach pain and nausea":
            {{
              "suggestions": [
                {{"specialty": "GASTROENTEROLOGY", "reason": "Persistent stomach pain and nausea indicate possible gastrointestinal disorders requiring specialist evaluation"}},
                {{"specialty": "GENERAL MEDICINE", "reason": "Initial assessment of gastrointestinal symptoms can be managed by general medicine"}}
              ],
              "emergency_advice": ""
            }}
            """
            
            user_prompt = f"""
            Based on the comprehensive medical specialty guide and clinical best practices, what specialty or specialties would be most appropriate for a patient with the following health issue or symptoms?
            
            Health problem/symptoms: {health_problem}
            
            EMERGENCY ALERT: {"This appears to be an emergency situation based on the symptoms described." if emergency_detection['is_emergency'] else "No immediate emergency symptoms detected."}
            
            Consider:
            - Symptom severity and potential emergency nature
            - Most appropriate specialty based on the detailed symptom guide
            - Available specialties at this hospital
            - Patient safety and optimal care pathway
            - IMPORTANT: Provide 2-3 specialty options when medically appropriate to give patients choices
            {"- URGENT: Include emergency advice as this may be a medical emergency" if emergency_detection['is_emergency'] else ""}
            
            Respond ONLY with the JSON format specified.
            """
            
            payload = {
                "model": "local-model",
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                "temperature": 0.1,  # Low temperature for consistent medical recommendations
                "max_tokens": 1000
            }
            
            # Log the request
            if self.debug_mode:
                logger.info(f"Sending request to LLM for specialist recommendation")
            
            # Make the request to the LLM
            response = requests.post(llm_endpoint, headers=headers, json=payload)
            response.raise_for_status()
            
            # Parse the response
            llm_response = response.json()
            recommendation_json = llm_response.get("choices", [{}])[0].get("message", {}).get("content", "")
            
            if not recommendation_json:
                logger.error("Empty recommendation from LLM")
                return {"error": "Failed to generate a recommendation", "action_type": "recommend_specialist"}
            
            # Log the recommendation
            if self.debug_mode:
                logger.info(f"LLM recommendation JSON: {recommendation_json}")
            
            # Parse the JSON recommendation
            try:
                recommendation_data = json.loads(recommendation_json)
                
                # Override or enhance emergency advice if we detected emergency symptoms
                if emergency_detection['is_emergency']:
                    # Use our comprehensive emergency advice
                    recommendation_data['emergency_advice'] = emergency_detection['advice']
                    logger.info(f"Emergency detected: {emergency_detection['emergency_type']}")
                    logger.info(f"Using comprehensive emergency advice: {emergency_detection['advice']}")
                elif not recommendation_data.get('emergency_advice'):
                    # If LLM didn't provide emergency advice but we have general emergency advice, use it
                    recommendation_data['emergency_advice'] = ""
                
                # Filter suggested specialties against available hospital specialties
                available_specialty_names_lower = [s.get("DESCRIPTION", "").lower() for s in all_specialties]
                filtered_suggestions = []
                
                # Log all suggestions from LLM for debugging
                logger.info(f"Raw LLM suggestions: {recommendation_data.get('suggestions', [])}")
                
                # Create a mapping of lowercase specialty names to their original form and ID
                specialty_mapping = {}
                for specialty in all_specialties:
                    desc = specialty.get("DESCRIPTION", "")
                    identifier = specialty.get("IDENTIFIER", "")
                    if desc and identifier:
                        specialty_mapping[desc.lower()] = {
                            "original": desc,
                            "id": str(identifier)
                        }
                
                # Log all specialty mappings for debugging        
                logger.info(f"Available specialty mappings: {specialty_mapping.keys()}")
                
                # Keep track of specialty IDs we've already added to avoid duplicates
                used_specialty_ids = set()
                
                for suggestion in recommendation_data.get("suggestions", []):
                    specialty = suggestion.get("specialty", "").lower()
                    matched = False
                    matched_specialty = None
                    matched_id = None
                    
                    # Log the current specialty being processed
                    logger.info(f"Processing specialty suggestion: '{specialty}'")
                    
                    # Try exact match first (case insensitive)
                    if specialty in specialty_mapping:
                        matched = True
                        matched_specialty = specialty_mapping[specialty]["original"]
                        matched_id = specialty_mapping[specialty]["id"]
                    else:
                        # Special case for ENT
                        if ("ear" in specialty and "nose" in specialty and "throat" in specialty) or "ent" in specialty or "otolaryngology" in specialty:
                            for avail_spec_lower, mapping in specialty_mapping.items():
                                if "ear" in avail_spec_lower and "nose" in avail_spec_lower and "throat" in avail_spec_lower:
                                    matched = True
                                    matched_specialty = mapping["original"]
                                    matched_id = mapping["id"]
                                    break
                        # Special case for Orthopedics - distinguish between medicine and surgery
                        elif "orthopedic" in specialty or "orthopaedic" in specialty:
                            # Check if it's orthopedic medicine or orthopedic surgery
                            is_surgery = "surgery" in specialty.lower()
                            is_medicine = "medicine" in specialty.lower() or not is_surgery
                            
                            # Look for matching specialty based on whether it's medicine or surgery
                            for avail_spec_lower, mapping in specialty_mapping.items():
                                if "orthopaedic" in avail_spec_lower:
                                    # Match surgery with surgery
                                    if is_surgery and "surgery" in avail_spec_lower:
                                        matched = True
                                        matched_specialty = mapping["original"]
                                        matched_id = mapping["id"]
                                        break
                                    # Match medicine with medicine or use as generic match if that's all we have
                                    elif is_medicine and ("medicine" in avail_spec_lower or (not matched)):
                                        matched = True
                                        matched_specialty = mapping["original"]
                                        matched_id = mapping["id"]
                                        if "medicine" in avail_spec_lower:
                                            break  # Perfect match, stop looking
                        # Special case for Emergency
                        elif "emergency" in specialty:
                            for avail_spec_lower, mapping in specialty_mapping.items():
                                if "emergency" in avail_spec_lower or "accident" in avail_spec_lower:
                                    matched = True
                                    matched_specialty = mapping["original"]
                                    matched_id = mapping["id"]
                                    break
                        # Special cases for common specialties that might have abbreviations
                        elif "neuro" in specialty and "surg" not in specialty:
                            for avail_spec_lower, mapping in specialty_mapping.items():
                                if "neurology" in avail_spec_lower:
                                    matched = True
                                    matched_specialty = mapping["original"]
                                    matched_id = mapping["id"]
                                    break
                        elif "neuro" in specialty and "surg" in specialty:
                            for avail_spec_lower, mapping in specialty_mapping.items():
                                if "neurosurgery" in avail_spec_lower:
                                    matched = True
                                    matched_specialty = mapping["original"]
                                    matched_id = mapping["id"]
                                    break
                        elif "cardio" in specialty:
                            for avail_spec_lower, mapping in specialty_mapping.items():
                                if "cardio" in avail_spec_lower:
                                    matched = True
                                    matched_specialty = mapping["original"]
                                    matched_id = mapping["id"]
                                    break
                        elif "general" in specialty and "med" in specialty:
                            for avail_spec_lower, mapping in specialty_mapping.items():
                                if "general medicine" in avail_spec_lower:
                                    matched = True
                                    matched_specialty = mapping["original"]
                                    matched_id = mapping["id"]
                                    break
                        # Special case for Outpatient Services (OPS/OPST)
                        elif "ops" in specialty.lower() or "opst" in specialty.lower() or "outpatient" in specialty.lower():
                            for avail_spec_lower, mapping in specialty_mapping.items():
                                if "ops" in avail_spec_lower or "outpatient" in avail_spec_lower:
                                    matched = True
                                    matched_specialty = mapping["original"]
                                    matched_id = mapping["id"]
                                    break
                        # Special case for Gastroenterology
                        elif "gastro" in specialty.lower():
                            for avail_spec_lower, mapping in specialty_mapping.items():
                                if "gastro" in avail_spec_lower:
                                    matched = True
                                    matched_specialty = mapping["original"]
                                    matched_id = mapping["id"]
                                    break
                        # Special case for Dermatology
                        elif "derma" in specialty.lower() or "skin" in specialty.lower():
                            for avail_spec_lower, mapping in specialty_mapping.items():
                                if "derma" in avail_spec_lower:
                                    matched = True
                                    matched_specialty = mapping["original"]
                                    matched_id = mapping["id"]
                                    break
                        # Special case for Pediatrics
                        elif "pedia" in specialty.lower() or "child" in specialty.lower():
                            for avail_spec_lower, mapping in specialty_mapping.items():
                                if "pedia" in avail_spec_lower:
                                    matched = True
                                    matched_specialty = mapping["original"]
                                    matched_id = mapping["id"]
                                    break
                        else:
                            # Generic contains match
                            for avail_spec_lower, mapping in specialty_mapping.items():
                                specialty_words = specialty.split()
                                for word in specialty_words:
                                    if len(word) > 3 and word in avail_spec_lower:
                                        matched = True
                                        matched_specialty = mapping["original"]
                                        matched_id = mapping["id"]
                                        break
                                if matched:
                                    break
                    
                    if matched and matched_specialty and matched_id:
                        # Check if we've already added this specialty ID to avoid duplicates
                        if matched_id not in used_specialty_ids:
                            # Create a new suggestion with the official hospital specialty name and ID
                            new_suggestion = suggestion.copy()  # Copy the original suggestion
                            new_suggestion["specialty"] = matched_specialty
                            new_suggestion["specialty_id"] = matched_id
                            filtered_suggestions.append(new_suggestion)
                            used_specialty_ids.add(matched_id)
                            logger.info(f"Added specialty '{suggestion.get('specialty')}' as '{matched_specialty}' with ID {matched_id}")
                        else:
                            # We've seen this specialty ID before
                            logger.info(f"Skipping duplicate specialty '{suggestion.get('specialty')}' (maps to already used ID {matched_id})")
            
            except json.JSONDecodeError:
                logger.error(f"Failed to parse LLM response as JSON: {recommendation_json}")
                return {"error": "Failed to parse recommendation format", "action_type": "recommend_specialist"}
            
            # If no matches found but we have General Medicine, use that as a fallback
            if not filtered_suggestions and recommendation_data.get("suggestions"):
                for avail_spec_lower, mapping in specialty_mapping.items():
                    if "general" in avail_spec_lower and "medicine" in avail_spec_lower:
                        filtered_suggestions.append({
                            "specialty": mapping["original"],
                            "specialty_id": mapping["id"],
                            "reason": "General practitioner for initial assessment and potential referral"
                        })
                        logger.info(f"No matches found, using General Medicine as fallback with ID {mapping['id']}")
                        break
            
            # Additional fallback: if we have fewer than 2 specialties but the LLM recommended more,
            # try a more aggressive matching approach for the unmatched suggestions
            if len(filtered_suggestions) < 2 and len(recommendation_data.get("suggestions", [])) > len(filtered_suggestions):
                logger.info(f"Only found {len(filtered_suggestions)} specialties, trying more aggressive matching")
                
                # Track which suggestions we've already matched
                matched_suggestions = set()
                for suggestion in filtered_suggestions:
                    matched_suggestions.add(suggestion.get("specialty", "").lower())
                
                # Try to match remaining suggestions with more flexible criteria
                for suggestion in recommendation_data.get("suggestions", []):
                    specialty = suggestion.get("specialty", "").lower()
                    
                    # Skip if we've already matched this specialty
                    if specialty in matched_suggestions:
                        continue
                    
                    # Try a more flexible match
                    matched = False
                    matched_specialty = None
                    matched_id = None
                    
                    # Try partial word matching
                    for avail_spec_lower, mapping in specialty_mapping.items():
                        # Skip specialties we've already added
                        if mapping["original"].lower() in matched_suggestions:
                            continue
                            
                        # Check if any word in the specialty is in the available specialty
                        specialty_words = specialty.split()
                        for word in specialty_words:
                            if len(word) > 2 and word in avail_spec_lower:
                                matched = True
                                matched_specialty = mapping["original"]
                                matched_id = mapping["id"]
                                break
                        
                        if matched:
                            break
                    
                    # If we found a match, add it
                    if matched and matched_specialty and matched_id and matched_id not in used_specialty_ids:
                        new_suggestion = suggestion.copy()
                        new_suggestion["specialty"] = matched_specialty
                        new_suggestion["specialty_id"] = matched_id
                        filtered_suggestions.append(new_suggestion)
                        used_specialty_ids.add(matched_id)
                        matched_suggestions.add(matched_specialty.lower())
                        logger.info(f"Added specialty '{suggestion.get('specialty')}' as '{matched_specialty}' with ID {matched_id} (flexible match)")
            
            # If we still have no matches but have original suggestions, add Accident & Emergency as a fallback
            if not filtered_suggestions and recommendation_data.get("suggestions"):
                for avail_spec_lower, mapping in specialty_mapping.items():
                    if "accident" in avail_spec_lower or "emergency" in avail_spec_lower:
                        filtered_suggestions.append({
                            "specialty": mapping["original"],
                            "specialty_id": mapping["id"],
                            "reason": "For initial assessment and triage of your symptoms"
                        })
                        logger.info(f"No matches found, using Emergency as fallback with ID {mapping['id']}")
                        break
            
            return {
                "action_type": "recommend_specialist",
                "filtered_suggestions": filtered_suggestions,
                "original_suggestions": recommendation_data.get("suggestions", []),
                "emergency_advice": recommendation_data.get("emergency_advice", ""),
                "health_problem": health_problem
            }
            
        except Exception as e:
            logger.error(f"Error recommending specialist: {str(e)}")
            return {"error": f"Error recommending specialist: {str(e)}", "action_type": "recommend_specialist"}
    
    def get_doctor_specialties(self, parameters: Dict[str, str]) -> Dict[str, Any]:
        """
        Get doctor specialties from the API.
        
        Args:
            parameters: Parameters for the API call (e.g., {"query": "cardio"})
            
        Returns:
            Doctor specialty information
        """
        # Check if we have the API token
        if not self.specialty_api_token:
            logger.error("Specialty API token not provided")
            raise ValueError("Specialty API token not provided")
        
        try:
            logger.info(f"Making API request to {self.specialty_api_endpoint}")
            response = requests.get(
                self.specialty_api_endpoint, 
                headers=self.headers
            )
            response.raise_for_status()
            
            # For demonstration/debug, log the raw response
            if self.debug_mode:
                logger.info(f"Raw API response: {json.dumps(response.json(), indent=2)[:500]}...")
            
            # Get all specialties from the response
            all_specialties = response.json().get("Codes", {}).get("SPECIALITY", [])
            logger.info(f"Retrieved {len(all_specialties)} specialties from API")
            
            # If a query parameter is provided, filter the results
            query = parameters.get("query", "").upper()
            
            # Check if this is explicitly marked as a full list request in parameters
            is_full_list = parameters.get("is_full_list", False)
            
            # Also check if the query suggests it's a request for the full list
            full_list_terms = ["FULL", "ALL", "COMPLETE", "YES", "YEAH", "SURE", "LIST", "SHOW", "MORE"]
            is_full_list_request = any(term in query.split() for term in full_list_terms)
            
            # Check if this is a general query about available specialties
            general_query_terms = ["AVAILABLE", "LIST", "ALL", "WHAT", "WHICH", "HAVE", "OFFER", "CONTINUE", "NEXT"]
            is_general_query = any(term in query for term in general_query_terms)
            
            # For full list requests or general queries, return all specialties
            if is_full_list or is_full_list_request or is_general_query:
                logger.info("Returning all specialties (full list request or general query)")
                return {"specialties": all_specialties, "is_full_list": True, "action_type": "get_doctor_specialties"}
            
            # For specific specialty queries
            if query:
                # Extract query terms for more flexible matching
                query_terms = query.split()
                
                # Filter out common words that aren't helpful for matching
                stop_words = ["WHAT", "WHICH", "ARE", "IS", "THE", "DO", "DOES", "YOU", "HAVE", "AVAILABLE", "THERE", "ANY", "FOR", "A", "AN", "IN", "AT", "BY", "WITH", "ABOUT", "PLEASE", "CAN", "COULD", "WOULD"]
                query_terms = [term for term in query_terms if term not in stop_words]
                
                logger.info(f"Filtering specialties by query terms: {query_terms}")
                filtered_specialties = []
                
                # Check each specialty against each query term
                for specialty in all_specialties:
                    desc = specialty.get("DESCRIPTION", "").upper()
                    
                    # Match if any term is contained in the description
                    if any(term in desc for term in query_terms):
                        filtered_specialties.append(specialty)
                
                logger.info(f"Found {len(filtered_specialties)} matching specialties")
                return {"specialties": filtered_specialties, "action_type": "get_doctor_specialties"}
            else:
                # For empty queries, return all specialties
                logger.info("Returning all specialties (no specific terms)")
                return {"specialties": all_specialties, "action_type": "get_doctor_specialties"}
                
        except Exception as e:
            logger.error(f"Error calling specialty API: {str(e)}")
            return {"error": str(e), "action_type": "get_doctor_specialties"}
    
    def activate_sso(self, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Activate SSO for a user.
        
        Args:
            parameters: Optional parameters (not used in this function)
            
        Returns:
            SSO activation result
        """
        try:
            url = "http://eserver/api/visitmgmt/Accounts/ActivateSSO?Id=$2a$06$209Th1Z/ZBraqhPa2PIQDeM/7T65Y6Y6MRS6YjefwVomvFAuMwYtG"
            res = requests.get(url, headers={"accept": "*/*"})
            return res.json()
        except Exception as e:
            logger.error(f"Error activating SSO: {str(e)}")
            return {"error": str(e)}
    
    def search_by_id_number(self, parameters: Dict[str, str]) -> Dict[str, Any]:
        """
        Search for a patient by ID number.
        
        Args:
            parameters: Parameters for the search (e.g., {"id_number": "DD15021998"})
            
        Returns:
            Search results
        """
        try:
            id_number = parameters.get("id_number", "DD15021998")  # Default to example ID if not provided
            url = f"http://eserver/api/clinicaldocs/Codes/SearchText?CodeName=CHECKIDNO&text={id_number}"
            res = requests.get(url, headers=self.headers)
            return res.json()
        except Exception as e:
            logger.error(f"Error searching by ID number: {str(e)}")
            return {"error": str(e)}
    
    def get_today_appointments(self, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Get today's appointments.
        
        Args:
            parameters: Optional parameters (not used in this function)
            
        Returns:
            Today's appointments
        """
        try:
            url = "http://eserver/api/clinicaldocs/VisitDocs/GetRecordset?VisitId=3598&QueryName=GET_TODAYAPPTS"
            res = requests.get(url, headers=self.headers)
            return res.json()
        except Exception as e:
            logger.error(f"Error getting today's appointments: {str(e)}")
            return {"error": str(e)}
    
    def get_ongoing_visits(self, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Get ongoing visits.
        
        Args:
            parameters: Optional parameters (not used in this function)
            
        Returns:
            Ongoing visits
        """
        try:
            url = "http://eserver/api/clinicaldocs/VisitDocs/GetRecordset?VisitId=3598&QueryName=GET_ONGOINGVISITS"
            res = requests.get(url, headers=self.headers)
            return res.json()
        except Exception as e:
            logger.error(f"Error getting ongoing visits: {str(e)}")
            return {"error": str(e)}
    
    def init_appointments(self, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Initialize appointments API.
        
        Args:
            parameters: Optional parameters (not used in this function)
            
        Returns:
            Initialization data
        """
        try:
            url = "http://eserver/api/his/AppointmentsAPI/InitAll"
            res = requests.get(url, headers=self.headers)
            return res.json()
        except Exception as e:
            logger.error(f"Error initializing appointments: {str(e)}")
            return {"error": str(e)}
    
    def get_user_dataset(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get user dataset for appointments.
        
        Args:
            parameters: Parameters for the dataset query
                - date_from: Start date (default: current UTC date)
                - date_to: End date (default: current UTC date + 3 days)
                - resource_type: Resource type (default: 1)
                - specialty_id: Specialty ID (required)
                - resource_id: Resource ID (optional)
                - clinic_id: Clinic ID (optional)
                - from_time: From time (optional)
                - to_time: To time (optional)
                - query_name: Query name (default: APPTFINDRESO_AI)
            
        Returns:
            User dataset with doctor availability information
        """
        try:
            # Get the query name, defaulting to APPTFINDRESO_AI
            query_name = parameters.get("query_name", "APPTFINDRESO_AI")
            
            # Extract parameters with defaults (Singapore timezone UTC+8) - use current date only for accuracy
            current_date = pendulum.now("Asia/Singapore").format("YYYY-MM-DD")
            
            date_from = parameters.get("date_from", current_date)
            date_to = parameters.get("date_to", current_date)
            resource_type = parameters.get("resource_type", "1")
            
            # Verify that specialty_id is provided
            specialty_id = parameters.get("specialty_id")
            if not specialty_id:
                logger.error("No specialty_id provided for get_user_dataset")
                return {
                    "error": "No specialty_id provided",
                    "action_type": "get_user_dataset"
                }
            
            # Log the specialty ID we're using
            logger.info(f"Getting doctors for specialty ID: {specialty_id} from {date_from} to {date_to}")
            
            # Construct the request body for APPTFINDRESO_AI
            body = {
                "RESOURCETYPE": resource_type,
                "SPECIALITYID": specialty_id,
                "RESOURCEID": parameters.get("resource_id"),
                "CLINICID": parameters.get("clinic_id"),
                "FROMDATE": date_from,
                "TODATE": date_to,
                "FROM_TIME": parameters.get("from_time"),
                "TO_TIME": parameters.get("to_time")
            }
            
            # Define the API endpoint and headers
            url = f"http://eserver/api/his/AppointmentsAPI/GetUserDataset?QueryName={query_name}"
            headers = {
                "accept": "text/plain",
                "Authorization": f"Bearer {self.specialty_api_token}",
                "Content-Type": "application/json"
            }
            
            # Log the request details
            logger.info(f"Making API request to {url}")
            logger.info(f"Request body: {json.dumps(body)}")
            
            # Make the API call
            response = requests.post(url, headers=headers, json=body)
            response.raise_for_status()  # Raise an exception for HTTP errors
            
            # Parse the response
            api_result = response.json()
            logger.info(f"API response received successfully : {api_result}")
            
            # Get specialty name from the response or use a default
            specialty_name = "Unknown Specialty"
            if api_result and len(api_result) > 0:
                # Try to get specialty name from the first item
                specialty_name = api_result[0].get("SPECIALITY_DESC", "Unknown Specialty")
                logger.info(f"Found specialty name in response: {specialty_name}")
            
            # Process the API result to extract doctor information
            # Each session should be shown separately, not grouped by doctor
            available_doctors = []  # Use a list to show each session separately
            
            # The API returns a list of objects - each representing a session
            for session_data in api_result:
                resource_id = session_data.get("RESOURCEID")
                session_id = session_data.get("SESSIONID")
                
                # Format the doctor name properly
                if session_data.get('TITLE') and session_data.get('NAME'):
                    doctor_name = f"{session_data.get('TITLE')} {session_data.get('NAME')}"
                else:
                    doctor_name = session_data.get('NAME', 'Unknown Doctor')
                
                # Convert STARTTIME from UTC to Singapore time using Pendulum
                start_time_str = session_data.get("STARTTIME", "")
                if start_time_str:
                    try:
                        # Parse the datetime string with Pendulum (handles various formats automatically)
                        utc_time = pendulum.parse(start_time_str, tz="UTC")
                        singapore_time = utc_time.in_timezone("Asia/Singapore")
                        start_time_display = singapore_time.format("HH:mm")
                        start_time_full = singapore_time.to_iso8601_string()
                        logger.info(f"Converted STARTTIME: {start_time_str} → {start_time_full} Singapore")
                    except Exception as e:
                        logger.warning(f"Failed to convert start time {start_time_str}: {e}")
                        start_time_display = start_time_str.split("T")[1][:5] if "T" in start_time_str else start_time_str
                        start_time_full = start_time_str
                else:
                    start_time_display = ""
                    start_time_full = ""
                
                # Convert ENDTIME from UTC to Singapore time using Pendulum
                end_time_str = session_data.get("ENDTIME", "")
                if end_time_str:
                    try:
                        # Parse the datetime string with Pendulum (handles various formats automatically)
                        utc_time = pendulum.parse(end_time_str, tz="UTC")
                        singapore_time = utc_time.in_timezone("Asia/Singapore")
                        end_time_display = singapore_time.format("HH:mm")
                        end_time_full = singapore_time.to_iso8601_string()
                        logger.info(f"Converted ENDTIME: {end_time_str} → {end_time_full} Singapore")
                    except Exception as e:
                        logger.warning(f"Failed to convert end time {end_time_str}: {e}")
                        end_time_display = end_time_str.split("T")[1][:5] if "T" in end_time_str else end_time_str
                        end_time_full = end_time_str
                else:
                    end_time_display = ""
                    end_time_full = ""

                # Get session date
                session_date = session_data.get("SESSDATE", "").split("T")[0] if "T" in session_data.get("SESSDATE", "") else session_data.get("SESSDATE", "")
                
                # Create a separate entry for each session
                session_entry = {
                    "name": doctor_name,
                    "specialty": specialty_name,
                    "specialty_id": specialty_id,
                    "resource_id": str(resource_id),
                    "session_id": str(session_id),
                    "session_date": session_date,
                    "session_desc": session_data.get("SESSIONDESC", ""),
                    "start_time": start_time_full,  # Full datetime in Singapore timezone
                    "start_time_display": start_time_display,  # Time only for display
                    "end_time": end_time_full,  # Full datetime in Singapore timezone  
                    "end_time_display": end_time_display,  # Time only for display
                    "available_slots": session_data.get("AVAILABLE", 0),
                    "total_slots": session_data.get("TOTAL", 0),
                    "booked_slots": session_data.get("BOOKED", 0)
                }
                
                # Log the conversion for debugging
                if start_time_str and "T" in start_time_str:
                    logger.info(f"Created session entry: {doctor_name} - {session_data.get('SESSIONDESC', '')} ({start_time_display}) with {session_data.get('AVAILABLE', 0)} slots")
                
                available_doctors.append(session_entry)
            
            # Sort sessions by doctor name, then by start time
            available_doctors.sort(key=lambda x: (x["name"], x["start_time"]))
            
            # The list is already ready to use
            doctors_list = available_doctors
            
            # Create result structure
            result = {
                "action_type": "get_user_dataset",
                "available_doctors": doctors_list,
                "specialty_name": specialty_name,
                "specialty_id": specialty_id,
                "date_range": f"{date_from} to {date_to}",
                "doctor_count": len(doctors_list),
                "type": "doctor_availability"
            }
            
            logger.info(f"Found {len(doctors_list)} doctors for specialty {specialty_name}")
            return result
            
        except Exception as e:
            logger.error(f"Error getting user dataset: {str(e)}")
            return {"error": str(e), "type": "error", "action_type": "get_user_dataset"}
    
    def get_session_slots(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get appointment session slots with timezone conversion.
        
        Args:
            parameters: Parameters for the slots query
                - resource_id: Resource ID of the doctor
                - session_date: Session date (YYYY-MM-DD format, default: current Singapore date)
                - session_id: Session ID
            
        Returns:
            Session slots with times converted to Singapore timezone
        """
        try:
            # Get current Singapore date as default
            default_date = pendulum.now("Asia/Singapore").format("YYYY-MM-DD")
            
            resource_id = parameters.get("resource_id", "175")
            session_date = parameters.get("session_date", default_date)
            session_id = parameters.get("session_id", "999")
            
            # Handle session_date - it might already be in ISO format from frontend
            if session_date.endswith('T00:00:00.000Z'):
                # Already in ISO format, use as-is
                formatted_session_date = session_date.replace(':', '%3A')
            else:
                # Just a date, append time
                formatted_session_date = f"{session_date}T00%3A00%3A00.000Z"
            
            url = f"http://eserver/api/his/AppointmentsAPI/GetSessionSlots?Id={resource_id}&SessionDate={formatted_session_date}&SessionId={session_id}"
            logger.info(f"Getting session slots for resource ID: {resource_id}, date: {session_date}, session ID: {session_id}")
            logger.info(f"Constructed URL: {url}")
            
            res = requests.get(url, headers=self.headers)
            result = res.json()
            
            # Log the raw API response for debugging
            logger.info(f"Raw API response for session slots: {result}")
            
            # Check if the result is a valid list of slots
            if not isinstance(result, list):
                logger.error(f"API returned invalid data type: {type(result)}, expected list")
                return {"error": "Invalid response format from API", "action_type": "get_session_slots"}
            
            # Check if the result contains valid slot data
            if len(result) > 0 and not isinstance(result[0], dict):
                logger.error(f"API returned invalid slot data format. First item: {result[0]}")
                logger.error(f"Expected dictionary, got {type(result[0])}")
                return {"error": "Invalid slot data format from API", "action_type": "get_session_slots"}
            
            # Convert slot times from UTC to Singapore timezone using Pendulum
            converted_slots = []
            for i, slot in enumerate(result):
                if isinstance(slot, dict):
                    converted_slot = slot.copy()
                    
                    # Convert STARTTIME if present
                    if 'STARTTIME' in converted_slot:
                        try:
                            start_time_str = converted_slot['STARTTIME']
                            # Parse with Pendulum and convert to Singapore timezone
                            utc_time = pendulum.parse(start_time_str, tz="UTC")
                            singapore_time = utc_time.in_timezone("Asia/Singapore")
                            converted_slot['STARTTIME'] = singapore_time.to_iso8601_string()
                            # Only log first few conversions to avoid spam
                            if i < 3:
                                logger.info(f"Converted STARTTIME from {slot['STARTTIME']} to {converted_slot['STARTTIME']}")
                        except Exception as time_err:
                            logger.warning(f"Failed to convert STARTTIME {converted_slot['STARTTIME']}: {time_err}")
                    
                    # Convert ENDTIME if present
                    if 'ENDTIME' in converted_slot:
                        try:
                            end_time_str = converted_slot['ENDTIME']
                            # Parse with Pendulum and convert to Singapore timezone
                            utc_time = pendulum.parse(end_time_str, tz="UTC")
                            singapore_time = utc_time.in_timezone("Asia/Singapore")
                            converted_slot['ENDTIME'] = singapore_time.to_iso8601_string()
                            # Only log first few conversions to avoid spam
                            if i < 3:
                                logger.info(f"Converted ENDTIME from {slot['ENDTIME']} to {converted_slot['ENDTIME']}")
                        except Exception as time_err:
                            logger.warning(f"Failed to convert ENDTIME {converted_slot['ENDTIME']}: {time_err}")
                    
                    # Convert SLOTTIME if present (this is the key field for individual slots)
                    if 'SLOTTIME' in converted_slot:
                        try:
                            slot_time_str = converted_slot['SLOTTIME']
                            # Parse with Pendulum and convert to Singapore timezone
                            utc_time = pendulum.parse(slot_time_str, tz="UTC")
                            singapore_time = utc_time.in_timezone("Asia/Singapore")
                            
                            # Store both original UTC time (for API calls) and display time
                            converted_slot['SLOTTIME_ORIGINAL'] = slot_time_str  # Keep original UTC time for API
                            converted_slot['SLOTTIME'] = singapore_time.to_iso8601_string()  # Display time
                            
                            # Only log first few conversions to avoid spam
                            if i < 3:
                                logger.info(f"Converted SLOTTIME from {slot['SLOTTIME']} to {converted_slot['SLOTTIME']}")
                                logger.info(f"Set SLOTTIME_ORIGINAL to: {converted_slot['SLOTTIME_ORIGINAL']}")
                        except Exception as time_err:
                            logger.warning(f"Failed to convert SLOTTIME {converted_slot['SLOTTIME']}: {time_err}")
                    
                    converted_slots.append(converted_slot)
                else:
                    converted_slots.append(slot)
            
            logger.info(f"Converted {len(converted_slots)} slots to Singapore timezone")
            
            # Add action type for easier identification in the agent
            return {
                "action_type": "get_session_slots",
                "resource_id": resource_id,
                "session_date": session_date,
                "session_id": session_id,
                "slots": converted_slots
            }
        except Exception as e:
            logger.error(f"Error getting session slots: {str(e)}")
            return {"error": str(e), "action_type": "get_session_slots"}
    
    def create_walkin(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a walk-in appointment.
        
        Args:
            parameters: Parameters for creating the walk-in
                - resource_id: Resource ID (required)
                - session_id: Session ID (required)
                - session_date: Session date (required)
                - from_time: From time (required)
                - patient_id: Patient ID (default: 3601)
            
        Returns:
            Visit ID as integer or error dict
        """
        try:
            resource_id = parameters.get("resource_id")
            session_id = parameters.get("session_id")
            session_date = parameters.get("session_date")
            from_time = parameters.get("from_time")
            patient_id = parameters.get("patient_id", "3601")  # Default to 3601 as specified
            
            # Validate required parameters are present
            if not all([resource_id, session_id, session_date, from_time]):
                missing_params = [param for param, value in {
                    "resource_id": resource_id,
                    "session_id": session_id,
                    "session_date": session_date,
                    "from_time": from_time
                }.items() if not value]
                
                logger.error(f"Missing required parameters for create_walkin: {missing_params}")
                return {"error": f"Missing required parameters: {', '.join(missing_params)}"}
            
            # URL encode from_time if it contains colons or plus signs
            if ":" in from_time and "%3A" not in from_time:
                from_time = from_time.replace(":", "%3A")
            if "+" in from_time:
                from_time = from_time.replace("+", "%2B")
            
            # CreateWalkin API - NO IndividualId parameter needed
            url = f"http://eserver/api/his/AppointmentsAPI/CreateWalkin?ResourceId={resource_id}&SessionId={session_id}&SessionDate={session_date}&FromTime={from_time}&PatientId={patient_id}"
            
            logger.info(f"Creating walk-in appointment with URL: {url}")
            res = requests.get(url, headers=self.headers)
            
            if res.status_code == 200:
                # The response is a visit_id (integer)
                visit_id = res.text.strip()
                logger.info(f"Successfully created walk-in appointment. Visit ID: {visit_id}")
                # Convert to integer and return directly
                if visit_id.isdigit():
                    return int(visit_id)
                else:
                    return {"error": f"Invalid visit_id format: {visit_id}"}
            else:
                logger.error(f"Failed to create walk-in appointment. Status: {res.status_code}, Response: {res.text}")
                return {"error": f"Failed to create appointment. Status: {res.status_code}"}
                
        except Exception as e:
            logger.error(f"Error creating walk-in: {str(e)}")
            return {"error": str(e)}
    
    def get_appointment_number(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get appointment details using visit ID.
        
        Args:
            parameters: Parameters for the appointment query
                - visit_id: Visit ID (required)
            
        Returns:
            Appointment details including appointment number and patient information
        """
        try:
            visit_id = parameters.get("visit_id")
            
            if not visit_id:
                logger.error("Missing required parameter: visit_id")
                return {"error": "Missing required parameter: visit_id"}
            
            url = f"http://eserver/api/clinicaldocs/VisitDocs/GetRecordset?VisitId={visit_id}&QueryName=GET_APPTNO"
            
            logger.info(f"Getting appointment details with URL: {url}")
            res = requests.get(url, headers=self.headers)
            
            if res.status_code == 200:
                appointment_data = res.json()
                logger.info(f"Successfully retrieved appointment details for visit ID: {visit_id}")
                return {
                    "appointment_data": appointment_data,
                    "visit_id": visit_id,
                    "success": True
                }
            else:
                logger.error(f"Failed to get appointment details. Status: {res.status_code}, Response: {res.text}")
                return {"error": f"Failed to get appointment details. Status: {res.status_code}"}
                
        except Exception as e:
            logger.error(f"Error getting appointment number: {str(e)}")
            return {"error": str(e)}
    
    def create_visit(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a visit from an appointment.
        
        Args:
            parameters: Parameters for creating the visit
                - appointment_id: Appointment ID (default: 1820)
            
        Returns:
            Visit creation result
        """
        try:
            appointment_id = parameters.get("appointment_id", "1820")
            url = f"http://eserver/api/his/AppointmentsAPI/CreateVisit?AppointmentId={appointment_id}"
            res = requests.get(url, headers=self.headers)
            return res.json()
        except Exception as e:
            logger.error(f"Error creating visit: {str(e)}")
            return {"error": str(e)}
    
    def get_patient_journey(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get patient journey information including queue details.
        This requires a two-step process:
        1. Call CreateVisit API with appointment ID to get new visit ID
        2. Call GET_PATIENT_JOURNEY API with the new visit ID
        """
        try:
            appointment_id = parameters.get("appointment_id")
            
            if not appointment_id:
                return {"error": "Appointment ID is required"}
            
            logger.info(f"Getting patient journey for appointment ID: {appointment_id}")
            
            # Step 1: Call CreateVisit API with appointment ID
            create_visit_url = f"http://eserver/api/his/AppointmentsAPI/CreateVisit?AppointmentId={appointment_id}"
            
            headers = {
                "accept": "text/plain",
                "Authorization": f"Bearer {self.specialty_api_token}"
            }
            
            logger.info(f"Calling CreateVisit API: {create_visit_url}")
            create_visit_response = requests.get(create_visit_url, headers=headers)
            
            if create_visit_response.status_code != 200:
                logger.error(f"Failed to create visit. Status code: {create_visit_response.status_code}")
                return {
                    "error": f"Failed to create visit. Status code: {create_visit_response.status_code}",
                    "status_code": create_visit_response.status_code
                }
            
            # CreateVisit API returns just a number (visit ID)
            new_visit_id = create_visit_response.text.strip()
            logger.info(f"CreateVisit API returned visit ID: {new_visit_id}")
            
            # Step 2: Call GET_PATIENT_JOURNEY API with the new visit ID
            journey_url = f"http://eserver/api/clinicaldocs/VisitDocs/GetRecordset?VisitId={new_visit_id}&QueryName=GET_PATIENT_JOURNEY"
            
            logger.info(f"Calling patient journey API: {journey_url}")
            journey_response = requests.get(journey_url, headers=headers)
            
            if journey_response.status_code == 200:
                journey_data = journey_response.json()
                logger.info(f"Successfully retrieved patient journey: {journey_data}")
                
                # Return the journey data with success flag
                return {
                    "success": True,
                    "journey_data": journey_data,
                    "visit_id": new_visit_id,
                    "appointment_id": appointment_id
                }
            else:
                logger.error(f"Failed to get patient journey. Status code: {journey_response.status_code}")
                return {
                    "error": f"Failed to get patient journey. Status code: {journey_response.status_code}",
                    "status_code": journey_response.status_code
                }
                
        except Exception as e:
            logger.error(f"Error getting patient journey: {str(e)}")
            return {
                "error": f"Failed to get patient journey information: {str(e)}"
            }

    def get_appointment_followup(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get appointment followup information
        """
        try:
            # Log the attempt
            logger.info("Getting appointment followup information")
            
            # For now, return a placeholder response
            return {
                "followup_info": "Appointment followup information will be available after your visit.",
                "next_steps": "Please arrive 10 minutes before your appointment time."
            }
            
        except Exception as e:
            logger.error(f"Error getting appointment followup: {str(e)}")
            return {
                "error": f"Failed to get appointment followup information: {str(e)}"
            }
    
    def _detect_emergency_symptoms(self, health_problem: str) -> dict:
        """
        Detect emergency symptoms and return appropriate emergency advice.
        
        Args:
            health_problem: The health problem or symptoms described by the user
            
        Returns:
            Dictionary with emergency status and advice
        """
        health_problem_lower = health_problem.lower()
        
        # Define emergency symptom patterns and their corresponding advice
        emergency_patterns = {
            # Cardiac emergencies
            'chest_pain': {
                'patterns': ['chest pain', 'chest discomfort', 'chest tightness', 'chest pressure', 'heart pain', 'cardiac pain'],
                'advice': "🚨 **URGENT**: Chest pain can be a sign of a heart attack. If you have severe chest pain, especially with shortness of breath, sweating, nausea, or arm pain, call emergency services immediately or go to the nearest emergency room."
            },
            
            # Neurological emergencies
            'severe_headache': {
                'patterns': ['severe headache', 'worst headache', 'sudden headache', 'thunderclap headache', 'headache of my life'],
                'advice': "🚨 **URGENT**: Sudden severe headache (especially if described as 'worst headache of your life') may indicate a serious condition like stroke or brain hemorrhage. Seek immediate emergency care."
            },
            
            'stroke_symptoms': {
                'patterns': ['sudden weakness', 'facial droop', 'slurred speech', 'vision loss', 'sudden numbness', 'can\'t speak', 'face drooping', 'arm weakness', 'speech difficulty'],
                'advice': "🚨 **URGENT**: These symptoms may indicate a stroke. Time is critical - call emergency services immediately. Remember F.A.S.T: Face drooping, Arm weakness, Speech difficulty, Time to call emergency services."
            },
            
            # Respiratory emergencies
            'breathing_difficulty': {
                'patterns': ['shortness of breath', 'difficulty breathing', 'can\'t breathe', 'trouble breathing', 'dyspnea', 'breathless', 'gasping'],
                'advice': "🚨 **URGENT**: Severe breathing difficulty requires immediate medical attention. If you cannot speak in full sentences due to breathlessness, call emergency services now."
            },
            
            # Abdominal emergencies
            'severe_abdominal_pain': {
                'patterns': ['severe abdominal pain', 'severe stomach pain', 'sudden abdominal pain', 'excruciating abdominal pain', 'severe belly pain'],
                'advice': "🚨 **URGENT**: Severe abdominal pain, especially with sudden onset, may indicate a surgical emergency. Seek immediate medical attention if the pain is severe or worsening."
            },
            
            # Bleeding emergencies
            'severe_bleeding': {
                'patterns': ['uncontrolled bleeding', 'heavy bleeding', 'bleeding heavily', 'blood loss', 'hemorrhage', 'bleeding won\'t stop'],
                'advice': "🚨 **URGENT**: Uncontrolled bleeding requires immediate emergency care. Apply direct pressure if safe to do so and call emergency services immediately."
            },
            
            # Consciousness emergencies
            'loss_consciousness': {
                'patterns': ['loss of consciousness', 'fainting', 'syncope', 'passed out', 'blacked out', 'unconscious'],
                'advice': "🚨 **URGENT**: Loss of consciousness or fainting may indicate a serious underlying condition. Seek immediate medical evaluation, especially if recurrent or associated with other symptoms."
            },
            
            # Seizure emergencies
            'seizures': {
                'patterns': ['seizure', 'convulsions', 'fitting', 'epileptic fit', 'shaking uncontrollably'],
                'advice': "🚨 **URGENT**: Seizures, especially first-time or prolonged seizures, require immediate emergency care. Call emergency services if seizure lasts more than 5 minutes or if it's the first seizure."
            },
            
            # Infection emergencies
            'severe_infection': {
                'patterns': ['high fever with stiff neck', 'fever with rash', 'fever with confusion', 'meningitis', 'sepsis'],
                'advice': "🚨 **URGENT**: High fever with stiff neck, rash, or altered mental status may indicate serious infection like meningitis. Seek immediate emergency care."
            },
            
            # Allergic emergencies
            'anaphylaxis': {
                'patterns': ['severe allergic reaction', 'anaphylaxis', 'difficulty breathing after exposure', 'throat swelling', 'widespread rash with breathing difficulty'],
                'advice': "🚨 **URGENT**: Signs of severe allergic reaction (anaphylaxis) include difficulty breathing, throat swelling, widespread rash, and rapid pulse. Use epinephrine if available and call emergency services immediately."
            },
            
            # Vision emergencies
            'vision_loss': {
                'patterns': ['sudden vision loss', 'vision loss', 'can\'t see', 'blind', 'lost vision', 'vision changes'],
                'advice': "🚨 **URGENT**: Sudden vision loss or significant vision changes require immediate medical attention to prevent permanent vision loss."
            },
            
            # Burn emergencies
            'severe_burns': {
                'patterns': ['severe burn', 'large burn', 'deep burn', 'burn on face', 'burn on hands', 'burn on genitals'],
                'advice': "🚨 **URGENT**: Severe burns (large area, deep, or on face/hands/genitals) require immediate emergency care. Do not apply ice or home remedies - seek professional medical treatment immediately."
            },
            
            # Poisoning emergencies
            'poisoning': {
                'patterns': ['poisoning', 'overdose', 'took too much', 'accidental ingestion', 'drug overdose', 'poison'],
                'advice': "🚨 **URGENT**: Poisoning or overdose requires immediate emergency care. Call emergency services or poison control immediately. Do not induce vomiting unless instructed by medical professionals."
            }
        }
        
        # Check for emergency patterns
        for emergency_type, emergency_info in emergency_patterns.items():
            for pattern in emergency_info['patterns']:
                if pattern in health_problem_lower:
                    return {
                        'is_emergency': True,
                        'emergency_type': emergency_type,
                        'advice': emergency_info['advice']
                    }
        
        # Check for general emergency keywords
        general_emergency_keywords = ['emergency', 'urgent', 'severe', 'critical', 'life threatening', 'can\'t breathe', 'excruciating']
        for keyword in general_emergency_keywords:
            if keyword in health_problem_lower:
                return {
                    'is_emergency': True,
                    'emergency_type': 'general_emergency',
                    'advice': "🚨 **URGENT**: Based on your symptoms, this may require immediate medical attention. If you feel this is an emergency, please call emergency services or go to the nearest emergency room immediately."
                }
        
        return {
            'is_emergency': False,
            'emergency_type': None,
            'advice': ""
        } 