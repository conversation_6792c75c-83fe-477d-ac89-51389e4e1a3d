"""
ReAct Agent for hospital chatbot using the Reason-Act-Observe paradigm.
This agent interfaces with a local LLM and uses external tools like doctor specialty lookup.
It also maintains persistent memory of conversations using MongoDB.
"""

import json
import requests
import os
import logging
import re
import traceback
import uuid
import pendulum
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional

from tools.tools_manager import ToolsManager
from database.db_connector import DBConnector
from core.agent_methods import AgentMethodsMixin

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger("ReActAgent")

class ReActAgent(AgentMethodsMixin):
    """
    ReAct Agent for hospital chatbot using the Reason-Act-Observe paradigm.
    This agent interfaces with a local LLM and uses external tools like doctor specialty lookup.
    It also maintains persistent memory of conversations using MongoDB.
    """
    
    def __init__(self, llm_endpoint: str = "http://192.168.56.1:1234/v1/chat/completions", 
                 specialty_api_endpoint: str = "http://eserver/api/his/AppointmentsAPI/InitAll",
                 specialty_api_token: Optional[str] = None,
                 mongo_uri: Optional[str] = None,
                 debug_mode: bool = True):
        """
        Initialize the ReAct Agent with necessary configurations.
        
        Args:
            llm_endpoint: URL of the LLM API endpoint
            specialty_api_endpoint: URL of the specialty API endpoint
            specialty_api_token: API token for specialty API (optional)
            mongo_uri: MongoDB connection URI (optional)
            debug_mode: Enable debug mode for detailed logging
        """
        self.llm_endpoint = llm_endpoint
        self.specialty_api_endpoint = specialty_api_endpoint
        self.specialty_api_token = specialty_api_token
        self.debug_mode = debug_mode
        
        # Initialize MongoDB connection
        self.db = None
        if mongo_uri:
            self.db = DBConnector(mongo_uri=mongo_uri)
        
        # Initialize tools
        self.tools_manager = ToolsManager(
            specialty_api_endpoint=specialty_api_endpoint,
            specialty_api_token=self.specialty_api_token,
            debug_mode=debug_mode
        )
        
        # Available tools mapping - mapping tool names to methods in Tools class
        self.tools = {
            "get_doctor_specialties": self.tools_manager.get_doctor_specialties,
            "activate_sso": self.tools_manager.activate_sso,
            "search_by_id_number": self.tools_manager.search_by_id_number,
            "get_today_appointments": self.tools_manager.get_today_appointments,
            "get_ongoing_visits": self.tools_manager.get_ongoing_visits,
            "init_appointments": self.tools_manager.init_appointments,
            "get_user_dataset": self.tools_manager.get_user_dataset,
            "get_session_slots": self.tools_manager.get_session_slots,
            "create_walkin": self.tools_manager.create_walkin,
            "get_appointment_number": self.tools_manager.get_appointment_number,
            "create_visit": self.tools_manager.create_visit,
            "get_patient_journey": self.tools_manager.get_patient_journey,
            "get_appointment_followup": self.tools_manager.get_appointment_followup,
            "recommend_specialist": self.tools_manager.recommend_specialist
        }
        
        # Initialize conversation history
        self.conversation_history = []
        
        # Initialize specialty memory and tracking
        self.specialty_memory = {}
        self.last_recommended_specialty = None
        
        # Add pending date request state
        self.pending_date_request = {}  # session_id -> {"specialty_id": id, "specialty_name": name}
        
        # Initialize user specified date storage for health problem queries with dates
        self.user_specified_date = {}  # session_id -> date_string
        
        # Current session ID - will be set when chat() is called
        self.current_session_id = None
        
        # Memory importance thresholds
        self.memory_importance_thresholds = {
            "greeting": 1,          # Low importance
            "specialty_info": 3,    # Medium importance
            "health_problem": 4,    # High importance  
            "appointment": 4,       # High importance
            "patient_info": 5       # Critical importance
        }
        
        # Keywords related to doctor specialties for better detection
        self.specialty_keywords = [
            "doctor", "specialist", "specialty", "specialties", "speciality", 
            "specialities", "department", "medical", "physician", "practitioner",
            "cardio", "heart", "dental", "teeth", "dentist", "neuro", "brain", 
            "ortho", "bone", "pediatric", "children", "emergency", "surgery"
        ]
        
        # Keywords related to appointments for better detection
        self.appointment_keywords = [
            "appointment", "book", "schedule", "slot", "reserve", "visit", 
            "consultation", "meet", "session", "timing", "available", 
            "follow-up", "followup", "checkup", "walkin", "walk-in"
        ]
        
        # Keywords related to health problems/symptoms for better detection
        self.health_problem_keywords = [
            "pain", "ache", "sick", "problem", "issue", "symptom", "condition",
            "suffering", "hurts", "hurt", "suffer", "ailment", "disease",
            "diagnosis", "treatment", "cure", "remedy", "health", "medical",
            "feeling", "ill", "unwell", "have", "experiencing", "worry"
        ]
        
        logger.info("ReAct Agent initialized with debug_mode=%s", debug_mode)

    def initialize_session(self, session_id: Optional[str] = None, user_data: Optional[Dict[str, Any]] = None) -> str:
        """
        Initialize or resume a session.

        Args:
            session_id: Existing session ID or None to create new session
            user_data: Optional user metadata

        Returns:
            Session ID (either existing or newly created)
        """
        # If no session ID provided, create a new one
        if not session_id:
            session_id = str(uuid.uuid4())
            logger.info(f"Generated new session ID: {session_id}")

        # Store session ID
        self.current_session_id = session_id

        # Clear conversation history for this session
        self.conversation_history = []

        # Try to create session in database
        if self.db and self.db.is_connected():
            # Check if session already exists
            existing_memories = self.db.retrieve_memories(session_id, limit=1)
            if existing_memories:
                logger.info(f"Resuming existing session: {session_id}")
                self._load_memories_into_context(session_id)
            else:
                # Create new session in database
                self.db.create_session(session_id, user_data)
                logger.info(f"Created new session in database: {session_id}")

                # Store initial greeting memory
                self._store_memory("greeting", "Session initialized", 1)

        return session_id

    def _load_memories_into_context(self, session_id: str) -> None:
        """
        Load relevant memories from database into agent context.

        Args:
            session_id: Session ID to load memories for
        """
        if not self.db or not self.db.is_connected():
            logger.warning("Database not connected, cannot load memories")
            return

        # Get all important memories for this session
        memories = self.db.retrieve_memories(session_id, limit=20)

        if not memories:
            logger.info("No memories found for session")
            return

        logger.info(f"Loading {len(memories)} memories into context")

        # Process specialty memories
        specialty_memories = [m for m in memories if m.get("memory_type") == "specialty_info"]
        for memory in specialty_memories:
            content = memory.get("content", "")
            metadata = memory.get("metadata", {})

            # If memory contains specialty mapping
            if "specialty_name" in metadata and "specialty_id" in metadata:
                specialty_name = metadata.get("specialty_name")
                specialty_id = metadata.get("specialty_id")
                if specialty_name and specialty_id:
                    logger.info(f"Loading specialty mapping from memory: {specialty_name} -> {specialty_id}")
                    self._store_specialty_mapping(specialty_name, specialty_id)

        # Process health problem memories
        health_memories = [m for m in memories if m.get("memory_type") == "health_problem"]
        if health_memories:
            latest_health_memory = health_memories[0]  # Most recent first
            content = latest_health_memory.get("content", "")
            logger.info(f"Loading health problem from memory: {content}")
            # Could be used later in reasoning

        # Process patient info memories
        patient_memories = [m for m in memories if m.get("memory_type") == "patient_info"]
        for memory in patient_memories:
            content = memory.get("content", "")
            logger.info(f"Loading patient info from memory: {content}")
            # Could be used to personalize responses

        # Update session activity
        self.db.update_session_activity(session_id)

    def _store_memory(self, memory_type: str, content: str,
                    importance: Optional[int] = None, metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Store a memory in the database.

        Args:
            memory_type: Type of memory (e.g., "specialty_info", "health_problem")
            content: Memory content
            importance: Memory importance (1-5)
            metadata: Additional metadata

        Returns:
            Memory ID if successful, empty string otherwise
        """
        if not self.db or not self.db.is_connected() or not self.current_session_id:
            return ""

        # Set default importance if not provided
        if importance is None:
            importance = self.memory_importance_thresholds.get(memory_type, 2)

        memory_id = self.db.store_memory(
            self.current_session_id,
            memory_type,
            content,
            importance,
            metadata
        )

        logger.info(f"Stored {memory_type} memory (importance: {importance}): {content[:50]}...")
        return memory_id

    def _store_specialty_memory(self, specialty_name: str, specialty_id: str) -> None:
        """
        Store specialty information in memory.

        Args:
            specialty_name: Name of specialty
            specialty_id: ID of specialty
        """
        metadata = {
            "specialty_name": specialty_name,
            "specialty_id": specialty_id
        }

        content = f"Patient is interested in {specialty_name} specialty (ID: {specialty_id})"
        self._store_memory("specialty_info", content, 3, metadata)

    def _store_health_problem_memory(self, health_problem: str) -> None:
        """
        Store health problem information in memory.

        Args:
            health_problem: Health problem description
        """
        content = f"Patient mentioned health problem: {health_problem}"
        metadata = {
            "health_problem": health_problem
        }

        self._store_memory("health_problem", content, 4, metadata)

    def _store_specialty_mapping(self, specialty_name: str, specialty_id: str) -> None:
        """
        Store specialty mapping in memory.

        Args:
            specialty_name: Name of specialty
            specialty_id: ID of specialty
        """
        self.specialty_memory[specialty_name] = specialty_id
        logger.info(f"Stored specialty mapping: {specialty_name} -> {specialty_id}")

    def chat(self, user_query: str, session_id: Optional[str] = None) -> str:
        """
        Process a user query through the ReAct (Reason-Act-Observe) flow.

        Args:
            user_query: The user's query string
            session_id: Optional session ID for conversation context

        Returns:
            The agent's response
        """
        try:
            # Initialize or resume session
            if session_id:
                self.current_session_id = session_id
                if self.db and self.db.is_connected():
                    # Load memories for existing session
                    self._load_memories_into_context(session_id)
            else:
                # Create new session
                session_id = self.initialize_session()

            logger.info(f"\n=== CHAT SESSION {session_id} ===")
            logger.info(f"User query: {user_query}")

            # Store user query in conversation history
            self.conversation_history.append({"role": "user", "content": user_query})

            # Step 1: Reason about the query
            reasoning_result = self._reason(user_query)

            if not reasoning_result.get("use_tool", False):
                # Direct answer without tool use
                response = reasoning_result.get("direct_answer", "I'm not sure how to help with that.")
                logger.info(f"Direct response: {response}")

                # Store response in conversation history
                self.conversation_history.append({"role": "assistant", "content": response})

                return response

            # Step 2: Act (use tool)
            action_result = self._act(reasoning_result.get("action", {}))

            # Step 3: Observe and format response
            final_response = self._observe(action_result, user_query)

            # Store response in conversation history
            self.conversation_history.append({"role": "assistant", "content": final_response})

            logger.info(f"Final response: {final_response}")
            return final_response

        except Exception as e:
            logger.error(f"Error in chat processing: {str(e)}")
            logger.error(traceback.format_exc())
            return "I'm sorry, I encountered an error while processing your request. Please try again."

    def _reason(self, user_query: str) -> Dict[str, Any]:
        """
        Reason about the next step based on the user query and conversation history.
        This method decides what action to take.

        Args:
            user_query: The user's input query

        Returns:
            Dict containing the reasoning and next action
        """
        logger.info("\n=== REASONING ===")

        # Check for greetings first
        if self._is_greeting(user_query):
            logger.info("Detected greeting or simple question, providing direct answer")
            return {
                "reasoning": "The user is providing a greeting or asking a simple question. I can answer directly without using a tool.",
                "use_tool": False,
                "direct_answer": self._get_greeting_response(user_query)
            }

        # Check for specialty selection
        if self._is_specialty_selection_query(user_query):
            logger.info("Detected specialty selection query")
            selected_specialty = self._extract_selected_specialty(user_query)

            if selected_specialty and selected_specialty in self.specialty_memory:
                specialty_id = self.specialty_memory[selected_specialty]
                current_date = pendulum.now("Asia/Singapore").format("YYYY-MM-DD")

                return {
                    "reasoning": f"The user has selected the {selected_specialty} specialty. I should retrieve available doctors for today ({current_date}).",
                    "use_tool": True,
                    "action": {
                        "action_type": "get_user_dataset",
                        "parameters": {
                            "resource_type": "1",
                            "specialty_id": specialty_id,
                            "date_from": current_date,
                            "date_to": current_date,
                            "query_name": "APPTFINDRESO_AI"
                        }
                    }
                }

        # Check for requests to list all specialties
        if self._is_list_all_specialties_query(user_query):
            logger.info("Detected request to list all specialties")
            return {
                "reasoning": "The user is requesting a full list of all available doctor specialties.",
                "use_tool": True,
                "action": {
                    "action_type": "get_doctor_specialties",
                    "parameters": {
                        "query": user_query,
                        "is_full_list": True,
                        "list_all": True
                    }
                }
            }

        # Check for health problem queries
        if self._is_health_problem_query(user_query):
            logger.info("Detected health problem question, using recommend_specialist tool")
            health_problem = self._extract_health_problem(user_query)

            if not health_problem or not health_problem.strip():
                return {
                    "reasoning": "The user's query seems to be about a health issue, but I couldn't extract a specific health problem.",
                    "use_tool": False,
                    "direct_answer": "To recommend the most appropriate specialist, could you please tell me more about your specific symptoms or health concerns?"
                }

            if self._is_vague_health_problem(health_problem):
                return {
                    "reasoning": "The user is asking about what specialist to see for a general health issue. I should ask for more specific symptoms.",
                    "use_tool": False,
                    "direct_answer": "To recommend the most appropriate specialist, could you please tell me more about your specific symptoms or health concerns?"
                }

            return {
                "reasoning": f"The user is describing a health problem: '{health_problem}'. I should recommend appropriate specialists.",
                "use_tool": True,
                "action": {
                    "action_type": "recommend_specialist",
                    "parameters": {
                        "health_problem": health_problem
                    }
                }
            }

        # Default fallback
        return {
            "reasoning": "I'm not sure how to help with this query. I should provide a helpful response.",
            "use_tool": False,
            "direct_answer": "I'm here to help you find the right medical specialist for your health concerns. Could you tell me about your symptoms or what type of specialist you're looking for?"
        }

    def _act(self, action: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the determined action using the appropriate tool.

        Args:
            action: Action dictionary containing action_type and parameters

        Returns:
            Result of the action
        """
        logger.info("\n=== ACTION ===")

        action_type = action.get("action_type")
        parameters = action.get("parameters", {})

        logger.info(f"Executing action: {action_type}")
        logger.info(f"Parameters: {parameters}")

        try:
            if action_type in self.tools:
                tool_function = self.tools[action_type]
                result = tool_function(parameters)
                logger.info(f"Tool result: {str(result)[:200]}...")
                return result
            else:
                logger.error(f"Unknown action type: {action_type}")
                return {"error": f"Unknown action type: {action_type}"}

        except Exception as e:
            logger.error(f"Error executing action {action_type}: {str(e)}")
            return {"error": f"Error executing action: {str(e)}"}

    def _observe(self, action_result: Dict[str, Any], user_query: str) -> str:
        """
        Process the action result and format a response for the user.

        Args:
            action_result: Result from the action
            user_query: Original user query for context

        Returns:
            Formatted response string
        """
        logger.info("\n=== OBSERVATION ===")

        try:
            # Handle errors
            if "error" in action_result:
                logger.error(f"Action resulted in error: {action_result['error']}")
                return "I'm sorry, I encountered an error while processing your request. Please try again or contact support if the problem persists."

            # Handle specialist recommendations
            if action_result.get("action_type") == "recommend_specialist":
                return self._format_specialist_recommendations(action_result)

            # Handle doctor specialties
            if action_result.get("action_type") == "get_doctor_specialties":
                return self._format_specialty_list(action_result)

            # Handle doctor availability
            if action_result.get("action_type") == "get_user_dataset":
                return self._format_doctor_availability(action_result)

            # Generic fallback
            return "I processed your request. Is there anything specific you'd like to know?"

        except Exception as e:
            logger.error(f"Error in observation: {str(e)}")
            return "I'm sorry, I'm having trouble processing your request at the moment. Could you try asking again?"

    def _format_specialist_recommendations(self, action_result: Dict[str, Any]) -> str:
        """Format specialist recommendation results."""
        filtered_suggestions = action_result.get("filtered_suggestions", [])
        emergency_advice = action_result.get("emergency_advice", "")
        health_problem = action_result.get("health_problem", "")

        if not filtered_suggestions:
            return "I'm sorry, I couldn't find any matching specialists for your concern. Please try describing your symptoms differently or contact our general medicine department."

        # Store specialty mappings for future reference
        for suggestion in filtered_suggestions:
            specialty_name = suggestion.get("specialty", "")
            specialty_id = suggestion.get("specialty_id", "")
            if specialty_name and specialty_id:
                self._store_specialty_mapping(specialty_name, specialty_id)

        # Format the response
        response = f"For {health_problem}, I recommend the following specialists:\n\n"

        for suggestion in filtered_suggestions:
            specialty = suggestion.get("specialty", "Unknown")
            reason = suggestion.get("reason", "")
            response += f"• **{specialty}**: {reason}\n"

        response += "\nPlease select a specialty you prefer, and I'll check doctor availability for you."

        # Add emergency advice if present
        if emergency_advice:
            response += f"\n\n⚠️ {emergency_advice}"

        return response

    def _format_specialty_list(self, action_result: Dict[str, Any]) -> str:
        """Format specialty list results."""
        specialties = action_result.get("specialties", [])

        if not specialties:
            return "I'm sorry, I couldn't retrieve the specialty list at the moment. Please try again later."

        # Store specialty mappings
        for specialty in specialties:
            name = specialty.get("DESCRIPTION", "")
            identifier = specialty.get("IDENTIFIER", "")
            if name and identifier:
                self._store_specialty_mapping(name, str(identifier))

        response = f"Here are the medical specialties available at our hospital:\n\n"

        # Format in columns for better readability
        specialty_names = [s.get("DESCRIPTION", "") for s in specialties if s.get("DESCRIPTION")]

        if len(specialty_names) > 15:
            # Create columns
            col_size = (len(specialty_names) + 2) // 3
            cols = []

            for i in range(0, len(specialty_names), col_size):
                col_items = specialty_names[i:i+col_size]
                cols.append("• " + "\n• ".join(col_items))

            response += "\n\n".join(cols)
        else:
            response += "• " + "\n• ".join(specialty_names)

        response += "\n\nWould you like me to help you find a specialist for a specific health concern?"

        return response

    def _format_doctor_availability(self, action_result: Dict[str, Any]) -> str:
        """Format doctor availability results."""
        # This is a simplified version - the full implementation would be more complex
        doctors = action_result.get("available_doctors", [])

        if not doctors:
            return "I'm sorry, but we couldn't find any doctors available for the requested date. Please try a different date or specialty."

        response = "Here are the available doctors:\n\n"

        for i, doctor in enumerate(doctors):
            name = doctor.get("name", "Unknown Doctor")
            start_time = doctor.get("start_time_display", "Unknown time")
            available_slots = doctor.get("available_slots", 0)

            response += f"• **Dr. {name}**\n"
            response += f"  {start_time}\n"
            response += f"  {available_slots} slots available\n\n"

        response += "Please select a doctor to see available slots."

        return response
