"""
Token management utilities for automatic token refresh.
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Optional
from config.settings import get_settings

logger = logging.getLogger("TokenManager")

class TokenManager:
    """Manages automatic token refresh for the application."""
    
    def __init__(self):
        self.settings = get_settings()
        self._refresh_task = None
        self._running = False
    
    async def start_auto_refresh(self):
        """Start automatic token refresh background task."""
        if self._running:
            logger.info("Token auto-refresh already running")
            return
        
        self._running = True
        self._refresh_task = asyncio.create_task(self._refresh_loop())
        logger.info("Started automatic token refresh")
    
    async def stop_auto_refresh(self):
        """Stop automatic token refresh background task."""
        self._running = False
        if self._refresh_task:
            self._refresh_task.cancel()
            try:
                await self._refresh_task
            except asyncio.CancelledError:
                pass
        logger.info("Stopped automatic token refresh")
    
    async def _refresh_loop(self):
        """Background loop to refresh token periodically."""
        while self._running:
            try:
                # Check every 30 minutes
                await asyncio.sleep(30 * 60)
                
                if self._running:  # Check if still running after sleep
                    logger.info("Checking if token needs refresh...")
                    refreshed = self.settings.refresh_token_if_needed()
                    if refreshed:
                        logger.info("Token refreshed automatically")
                    else:
                        logger.debug("Token still valid")
                        
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in token refresh loop: {str(e)}")
                # Continue the loop even if there's an error
                await asyncio.sleep(60)  # Wait 1 minute before retrying
    
    def get_current_token(self) -> Optional[str]:
        """Get the current valid token."""
        # Refresh if needed before returning
        self.settings.refresh_token_if_needed()
        return self.settings.specialty_api_token
    
    def get_token_status(self) -> dict:
        """Get current token status information."""
        return self.settings.get_token_info()

# Global token manager instance
_token_manager = None

def get_token_manager() -> TokenManager:
    """Get the global token manager instance."""
    global _token_manager
    if _token_manager is None:
        _token_manager = TokenManager()
    return _token_manager
