<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Athena Health Assistant</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        :root {
            --primary-color: #4287f5;
            --secondary-color: #34a853;
            --text-color: #333333;
            --light-bg: #f5f9ff;
            --border-color: #e0e5ee;
            --action-button-bg: #f9f9f9;
            --action-button-hover: #f0f7ff;
            --specialty-button-bg: #e3efff;
            --specialty-button-hover: #d4e6ff;
            --specialty-button-border: #b1d0ff;
            --user-message-bg: #e3efff;
            --agent-message-bg: #f7f7f7;
            --doctor-card-bg: #f9fbff;
            --doctor-card-border: #d1e3ff;
            --session-available-bg: #e7f5eb;
            --session-available-text: #2e7d32;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background-color: var(--light-bg);
            padding: 0;
            margin: 0;
            min-height: 100vh;
        }

        .container {
            max-width: 500px;
            margin: 0 auto;
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: linear-gradient(135deg, #4287f5, #5a9af8);
            padding: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .logo {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            font-size: 20px;
            margin-right: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }
        
        .header-text {
            font-size: 18px;
            font-weight: 600;
            color: white;
        }

        .close-button {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: white;
            transition: all 0.2s ease;
        }
        
        .close-button:hover {
            transform: scale(1.1);
        }
        
        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background-color: white;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234287f5' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .welcome-message {
            margin-bottom: 20px;
            animation: fadeIn 0.5s ease-out;
            padding: 15px;
            border-radius: 12px;
            background: linear-gradient(to bottom right, #f5f9ff, #e3efff);
            border: 1px solid var(--border-color);
        }

        .welcome-message h2 {
            font-size: 24px;
            margin-bottom: 8px;
            color: var(--primary-color);
            font-weight: 600;
        }

        .welcome-message p {
            font-size: 16px;
            color: #555;
            margin-bottom: 10px;
            line-height: 1.5;
        }

        .highlight {
            color: var(--secondary-color);
            font-weight: 600;
        }

        .quick-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
            margin-bottom: 20px;
        }

        .action-button {
            background-color: var(--action-button-bg);
            border: 1px solid var(--border-color);
            border-radius: 30px;
            padding: 8px 15px;
            font-size: 14px;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-button:hover {
            background-color: var(--action-button-hover);
        }

        .action-button i {
            margin-right: 8px;
            color: var(--primary-color);
        }

        #end-chat-btn {
            background-color: #fff0f0;
            border-color: #ffdddd;
        }
        
        #end-chat-btn:hover {
            background-color: #ffe5e5;
        }
        
        #end-chat-btn i {
            color: #e74c3c;
        }

        .message-container {
            display: flex;
            flex-direction: column;
            margin-bottom: 25px;
            width: 100%;
        }

        .message {
            margin-bottom: 8px;
            max-width: 85%;
            padding: 14px 18px;
            border-radius: 18px;
            line-height: 1.5;
            position: relative;
            box-shadow: 0 2px 6px rgba(0,0,0,0.08);
            font-size: 15px;
            overflow: hidden; /* Add this to contain floated children */
            display: flex;
            flex-direction: column;
        }
        
        /* Add a clearfix utility class */
        .clearfix::after {
            content: "";
            display: table;
            clear: both;
        }
        
        .user-message {
            background-color: var(--user-message-bg);
            color: #333;
            align-self: flex-end;
            border-bottom-right-radius: 5px;
            font-weight: 500;
            animation: slideInRight 0.3s ease-out;
        }
        
        .agent-message {
            background-color: var(--agent-message-bg);
            color: #333;
            align-self: flex-start;
            border-bottom-left-radius: 5px;
            animation: slideIn 0.3s ease-out;
        }

        .typing-indicator {
            display: flex;
            padding: 10px 15px;
            background-color: #f0f0f0;
            border-radius: 18px;
            border-bottom-left-radius: 5px;
            align-self: flex-start;
            margin-bottom: 15px;
        }

        .typing-indicator span {
            height: 8px;
            width: 8px;
            margin: 0 1px;
            background-color: #999;
            border-radius: 50%;
            display: inline-block;
            animation: typing-bounce 1.4s infinite ease-in-out both;
        }

        .typing-indicator span:nth-child(1) {
            animation-delay: -0.32s;
        }

        .typing-indicator span:nth-child(2) {
            animation-delay: -0.16s;
        }

        @keyframes typing-bounce {
            0%, 80%, 100% {
                transform: scale(0);
            }
            40% {
                transform: scale(1);
            }
        }

        .input-area {
            padding: 15px;
            background-color: white;
            border-top: 1px solid var(--border-color);
            position: relative;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
        }
        
        .input-container {
            display: flex;
            border: 1px solid #ddd;
            border-radius: 30px;
            overflow: hidden;
            padding: 0 5px;
            position: relative;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .input-container:focus-within {
            border-color: var(--primary-color);
            box-shadow: 0 2px 12px rgba(66, 135, 245, 0.15);
        }

        #user-input {
            flex: 1;
            padding: 14px 18px;
            border: none;
            outline: none;
            font-size: 15px;
            color: #333;
        }
        
        #user-input::placeholder {
            color: #aaa;
        }
        
        .send-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            width: 44px;
            height: 44px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            margin: 4px;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(66, 135, 245, 0.25);
        }
        
        .send-button:hover {
            background-color: #2a70dc;
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(66, 135, 245, 0.35);
        }
        
        .send-button:active {
            transform: scale(0.95);
        }
        
        .debug-container {
            padding: 10px 15px;
            border-top: 1px solid var(--border-color);
            background-color: #fafafa;
        }

        .debug-toggle {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .toggle-wrapper {
            display: flex;
            align-items: center;
        }
        
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 36px;
            height: 20px;
            margin-right: 8px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .3s;
            border-radius: 34px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 14px;
            width: 14px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .3s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: var(--primary-color);
        }
        
        input:checked + .toggle-slider:before {
            transform: translateX(16px);
        }

        .connection-status {
            font-size: 12px;
            color: #666;
            display: flex;
            align-items: center;
        }
        
        .status-indicator {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .status-connected {
            background-color: #34a853;
        }
        
        .status-disconnected {
            background-color: #ea4335;
        }

        .debug-log {
            display: none;
            height: 150px;
            overflow-y: auto;
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 10px;
            background-color: white;
            font-family: monospace;
            font-size: 12px;
        }
        
        .message + .message {
            margin-top: 16px;
        }

        /* New styles for specialty selection */
        .suggested-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 12px;
            margin-bottom: 5px;
            align-self: flex-start;
        }
        
        .specialty-button {
            background-color: var(--specialty-button-bg);
            border: 1px solid var(--specialty-button-border);
            border-radius: 15px;
            padding: 8px 15px;
            font-size: 14px;
            font-weight: 500;
            color: #333;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            text-align: center;
        }
        
        .specialty-button:hover {
            background-color: var(--specialty-button-hover);
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }
        
        .specialty-button i {
            margin-right: 5px;
            color: var(--primary-color);
        }
        
        /* Specialty action container */
        .specialty-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
            margin-bottom: 15px;
            padding: 15px;
            background-color: #f0f7ff;
            border-radius: 10px;
            border: 2px solid #4287f5;
            width: 100%;
            max-width: 450px;
            box-shadow: 0 4px 12px rgba(66, 135, 245, 0.15);
        }
        
        .specialty-actions-title {
            width: 100%;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            font-size: 16px;
            text-align: center;
        }
        
        /* Enhanced specialty selection buttons */
        .specialty-selection-container {
            display: flex;
            flex-direction: column;
            align-self: flex-start;
            background: linear-gradient(to bottom right, #f0f7ff, #e3efff);
            border-radius: 16px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #b1d0ff;
            padding: 20px;
            margin: 15px 0;
            width: 90%;
            max-width: 450px;
            animation: fadeIn 0.5s ease-out;
            transition: all 0.3s ease;
        }
        
        .specialty-selection-container:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }
        
        .specialty-selection-header {
            color: #333;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 14px;
            display: flex;
            align-items: center;
        }
        
        .specialty-selection-header i {
            margin-right: 10px;
            color: var(--primary-color);
            font-size: 20px;
        }
        
        .specialty-options {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-bottom: 12px;
        }
        
        .specialty-option-button {
            background-color: #4287f5;
            color: white;
            border: none;
            border-radius: 20px;
            padding: 14px 22px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(66, 135, 245, 0.25);
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }
        
        .specialty-option-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.2);
            transition: all 0.4s ease;
        }
        
        .specialty-option-button:hover::before {
            left: 100%;
        }
        
        .specialty-option-button:hover {
            background-color: #2a70dc;
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(66, 135, 245, 0.35);
        }
        
        .specialty-option-button:active {
            transform: translateY(1px);
            box-shadow: 0 2px 8px rgba(66, 135, 245, 0.2);
        }
        
        /* Add style for selected specialty button */
        .specialty-option-button.selected {
            background-color: #34a853;
            border: 2px solid #fff;
            box-shadow: 0 0 0 2px #34a853, 0 4px 10px rgba(52, 168, 83, 0.3);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(52, 168, 83, 0.4), 0 4px 10px rgba(52, 168, 83, 0.3);
            }
            70% {
                box-shadow: 0 0 0 8px rgba(52, 168, 83, 0), 0 4px 10px rgba(52, 168, 83, 0.3);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(52, 168, 83, 0), 0 4px 10px rgba(52, 168, 83, 0.3);
            }
        }
        
        .specialty-option-button i {
            margin-right: 10px;
            font-size: 18px;
        }

        /* New styles for structured doctor information */
        .doctor-card {
            background-color: var(--doctor-card-bg);
            border: 1px solid var(--doctor-card-border);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;
        }
        
        .doctor-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .doctor-name {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 10px;
            font-size: 16px;
            display: flex;
            align-items: center;
        }
        
        .doctor-name i {
            margin-right: 8px;
            color: var(--primary-color);
        }
        
        .session-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .session-item {
            background-color: var(--session-available-bg);
            color: var(--session-available-text);
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .session-item:hover {
            background-color: #d5eed9;
            transform: translateY(-1px);
        }
        
        .session-item.selected {
            background-color: #c5e8ca;
            border: 2px solid #2e7d32;
        }
        
        .session-info {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .session-time {
            font-size: 14px;
            color: #555;
            display: flex;
            align-items: center;
        }
        
        .session-time i {
            margin-right: 5px;
            color: var(--primary-color);
        }
        
        .session-date {
            font-weight: 500;
        }
        
        .session-slots {
            display: flex;
            align-items: center;
            font-weight: 600;
        }
        
        .session-slots i {
            margin-right: 5px;
        }
        
        .appointment-prompt {
            margin-top: 10px;
            font-style: italic;
            color: #666;
            font-size: 14px;
        }
        
        /* Time slots styling */
        .slots-container {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin: 15px 0;
            width: 100%;
        }
        
        .time-slot {
            padding: 10px;
            text-align: center;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .time-slot.available {
            background-color: #e3efff;
            color: #333;
            border: 1px solid #b1d0ff;
        }
        
        .time-slot.booked {
            background-color: #f0f0f0;
            color: #999;
            border: 1px solid #ddd;
            cursor: not-allowed;
            text-decoration: line-through;
        }
        
        .time-slot.selected {
            background-color: var(--primary-color);
            color: white;
            border: 1px solid #2a70dc;
            transform: scale(1.05);
            box-shadow: 0 2px 6px rgba(66, 135, 245, 0.3);
        }
        
        .time-slot.available:hover {
            background-color: #d4e6ff;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,0.15);
        }
        
        .book-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 30px;
            padding: 10px 20px;
            font-size: 15px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 15px;
            box-shadow: 0 2px 8px rgba(66, 135, 245, 0.25);
            width: 100%;
            max-width: 250px;
        }
        
        .book-button:hover:not([disabled]) {
            background-color: #2a70dc;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(66, 135, 245, 0.35);
        }
        
        .book-button:active:not([disabled]) {
            transform: translateY(1px);
        }
        
        .book-button[disabled] {
            background-color: #cccccc;
            cursor: not-allowed;
            box-shadow: none;
        }
        
        .book-button i {
            margin-right: 8px;
        }
        
        /* Modal styles for appointment booking */
        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            visibility: hidden;
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        .warning-message {
            color: #e74c3c;
            background-color: #fff0f0;
            border: 1px solid #ffdddd;
            border-radius: 8px;
            padding: 10px 15px;
            margin: 10px auto;
            text-align: center;
            font-weight: 500;
            max-width: 300px;
            animation: fadeIn 0.3s ease-out;
        }
        
        .warning-message i {
            margin-right: 6px;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* Enhanced message formatting */
        .message p {
            margin-bottom: 10px;
        }
        
        .message p:last-child {
            margin-bottom: 0;
        }
        
        .message ul {
            margin: 8px 0 8px 20px;
        }
        
        .message ul li {
            margin-bottom: 6px;
        }
        
        .message strong {
            font-weight: 600;
            color: #222;
        }
        
        .message-timestamp {
            font-size: 10px;
            color: #aaa;
            margin-top: 6px;
            text-align: right;
            padding: 2px 6px;
            font-weight: 400;
            letter-spacing: 0.2px;
            opacity: 0.8;
            display: block;
            border-radius: 10px;
            background-color: rgba(0, 0, 0, 0.03);
            white-space: nowrap;
            float: right;
            clear: both;
        }
        
        .user-message .message-timestamp {
            color: #7eabde;
            background-color: rgba(66, 135, 245, 0.05);
            margin-left: auto;
        }
        
        .agent-message .message-timestamp {
            color: #999;
            margin-right: auto;
        }
        
        /* Add subtle animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateX(-10px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(10px); }
            to { opacity: 1; transform: translateX(0); }
        }

        /* Styles for structured specialty categories */
        .specialty-categories {
            display: flex;
            flex-direction: column;
            gap: 16px;
            margin: 15px 0;
            width: 100%;
        }
        
        .specialty-category {
            background-color: white;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;
        }
        
        .specialty-category:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        
        .category-header {
            background: linear-gradient(to right, #4287f5, #5a9af8);
            color: white;
            padding: 12px 16px;
            font-weight: 600;
            font-size: 16px;
            display: flex;
            align-items: center;
        }
        
        .category-header i {
            margin-right: 10px;
            font-size: 18px;
        }
        
        .category-items {
            padding: 12px 16px;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .specialty-item {
            background-color: #f0f7ff;
            border-radius: 20px;
            padding: 8px 16px;
            font-size: 14px;
            color: #333;
            border: 1px solid #d1e3ff;
            display: inline-flex;
            align-items: center;
            transition: all 0.2s ease;
            cursor: pointer;
            position: relative;
            margin-bottom: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        .specialty-item:hover {
            background-color: #e3efff;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
            color: var(--primary-color);
            border-color: var(--primary-color);
            font-weight: 500;
            padding-right: 20px;
        }
        
        /* Remove hover ::after content */
        .specialty-item:hover::after {
            content: none;
        }
        
        /* Remove the always-visible select button */
        .specialty-item::after {
            content: none;
        }
        
        /* Add a right arrow icon on hover using ::before instead */
        .specialty-item:hover::before {
            content: "\f054"; /* Font Awesome arrow-right icon */
            font-family: "Font Awesome 5 Free";
            font-weight: 900;
            position: absolute;
            right: 10px;
            font-size: 10px;
            color: var(--primary-color);
            animation: bounceRight 1s infinite;
        }
        
        @keyframes bounceRight {
            0%, 100% { transform: translateX(0); }
            50% { transform: translateX(3px); }
        }
        
        .specialty-item:active {
            transform: translateY(1px);
            background-color: #d4e6ff;
        }
        
        .specialty-item i {
            margin-right: 8px;
            color: var(--primary-color);
            font-size: 12px;
        }
        
        .appointment-prompt {
            margin-top: 16px;
            font-style: italic;
            color: #555;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* Patient Information Form Styles */
        .patient-info-form {
            background-color: #f8fbff;
            border: 1px solid #d1e3ff;
            border-radius: 12px;
            padding: 16px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
        }
        
        .form-group {
            margin-bottom: 14px;
        }
        
        .form-group label {
            display: block;
            font-weight: 500;
            margin-bottom: 5px;
            color: #444;
            font-size: 14px;
        }
        
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d1e3ff;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .form-group input:focus, .form-group textarea:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(66, 135, 245, 0.2);
            outline: none;
        }
        
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        
        .submit-form-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 30px;
            padding: 12px 20px;
            font-size: 16px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            margin: 20px auto 5px;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(66, 135, 245, 0.25);
            width: 100%;
            max-width: 250px;
        }
        
        .submit-form-button:hover {
            background-color: #2a70dc;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(66, 135, 245, 0.35);
        }
        
        .submit-form-button:active {
            transform: translateY(1px);
        }
        
        .submit-form-button i {
            margin-right: 8px;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .time-slot {
            background-color: #f0f7ff;
            border: 1px solid #d1e3ff;
            border-radius: 6px;
            padding: 10px 15px;
            margin: 5px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
            color: #333;
            text-align: center;
            width: calc(33.33% - 10px);
            box-sizing: border-box;
        }
        
        .time-slot:hover {
            background-color: #e3efff;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
        }
        
        .time-slot.selected {
            background-color: #d4e6ff;
            border-color: #4287f5;
            color: #4287f5;
            font-weight: 500;
            box-shadow: 0 0 0 2px rgba(66, 135, 245, 0.2);
        }
        
        .slots-container {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin: 15px 0;
        }
        
        /* Slot header styling */
        .slot-header {
            background: linear-gradient(135deg, #4287f5, #5a9af8);
            color: white;
            padding: 15px;
            border-radius: 10px 10px 0 0;
            margin: -15px -15px 15px -15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .doctor-info {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .doctor-info .doctor-name {
            color: white;
            font-size: 18px;
            margin-bottom: 5px;
        }
        
        .doctor-info .session-date {
            font-size: 14px;
            opacity: 0.9;
        }
        
        /* Patient form styling */
        .patient-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-top: 20px;
            background-color: #f9fbff;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #d1e3ff;
        }
        
        .form-field {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .form-field label {
            font-weight: 500;
            font-size: 14px;
            color: #444;
        }
        
        .form-field input, .form-field textarea {
            padding: 10px;
            border: 1px solid #d1e3ff;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .form-field input:focus, .form-field textarea:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(66, 135, 245, 0.2);
            outline: none;
        }
        
        .form-field textarea {
            min-height: 80px;
            resize: vertical;
        }
        
        .submit-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 30px;
            padding: 12px 20px;
            font-size: 15px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 10px;
            box-shadow: 0 2px 8px rgba(66, 135, 245, 0.25);
        }
        
        .submit-button:hover {
            background-color: #2a70dc;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(66, 135, 245, 0.35);
        }
        
        .submit-button:active {
            transform: translateY(1px);
        }
        
        .submit-button i {
            margin-right: 8px;
        }

        /* Appointment confirmation styles */
        .appointment-confirmation {
            background: linear-gradient(to bottom, #f9fff9, #f0f8ff);
            border: 2px solid #4caf50;
            border-radius: 12px;
            margin: 15px 0;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.15);
        }

        .confirmation-header {
            background: #4caf50;
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .appointment-details {
            padding: 20px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e8f5e8;
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-row .label {
            font-weight: 600;
            color: #2e7d32;
            flex: 1;
        }

        .detail-row .value {
            font-weight: 500;
            color: #333;
            flex: 2;
            text-align: right;
        }

        .appointment-disclaimer {
            background: #e3f2fd;
            border-top: 1px solid #bbdefb;
            padding: 15px 20px;
            color: #1565c0;
            font-size: 14px;
            line-height: 1.4;
        }

        /* Simple single-column appointment confirmation card */
        .appointment-confirmation-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            margin: 15px 0;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 100%;
            width: 100%;
            box-sizing: border-box;
        }

        .confirmation-header {
            background: linear-gradient(135deg, #4caf50, #45a049);
            color: white;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .confirmation-header i {
            font-size: 28px;
            color: white;
        }

        .header-text h3 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
        }

        .header-text p {
            margin: 5px 0 0 0;
            opacity: 0.9;
            font-size: 14px;
        }

        .appointment-details {
            padding: 20px;
        }

        .detail-section {
            margin-bottom: 25px;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 20px;
        }

        .detail-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .detail-section h4 {
            margin: 0 0 18px 0;
            color: #333;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            padding-bottom: 8px;
            border-bottom: 2px solid #f0f0f0;
        }

        .detail-section h4 i {
            color: #2196f3;
            width: 20px;
        }

        .doa-toa-section h4 {
            color: #1976d2;
            font-weight: 700;
        }

        .doa-toa-section h4 i {
            color: #1976d2;
        }

        .detail-item {
            margin: 12px 0;
            line-height: 1.6;
            font-size: 14px;
            padding: 5px 0;
        }

        .detail-item strong {
            color: #555;
            display: inline-block;
            min-width: 100px;
            margin-right: 10px;
        }

        .orders-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .order-item {
            padding: 12px 15px;
            background: white;
            border: 1px solid #bbdefb;
            border-radius: 6px;
            text-align: center;
            font-size: 14px;
            font-weight: 500;
            color: #333;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .order-item strong {
            color: #1976d2;
            font-weight: 600;
        }

        .doa-toa-section {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .important-note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            display: flex;
            align-items: center;
            gap: 10px;
            color: #856404;
        }

        .important-note i {
            color: #ff9800;
            font-size: 18px;
        }

        .action-section {
            text-align: center;
            margin-top: 20px;
        }

        .registration-btn {
            background: #4caf50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: background 0.3s;
            font-size: 14px;
        }

        .registration-btn:hover {
            background: #45a049;
        }

        /* Patient Journey Card Styles */
        .patient-journey-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            margin: 15px 0;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 100%;
            width: 100%;
            box-sizing: border-box;
        }

        .journey-header {
            background: linear-gradient(135deg, #2196f3, #1976d2);
            color: white;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .journey-header i {
            font-size: 28px;
            color: white;
        }

        .journey-header .header-text h3 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
        }

        .journey-header .header-text p {
            margin: 5px 0 0 0;
            opacity: 0.9;
            font-size: 14px;
        }

        .queue-number {
            font-weight: 700;
            font-size: 18px;
            color: #ffeb3b;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .journey-details {
            padding: 20px;
        }

        .queue-section {
            background: #f0f7ff;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }

        .queue-section h4 {
            color: #1976d2;
            font-weight: 700;
        }

        .queue-section h4 i {
            color: #1976d2;
        }

        .queue-display {
            text-align: center;
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 2px solid #2196f3;
        }

        .queue-number-large {
            font-size: 48px;
            font-weight: 900;
            color: #1976d2;
            margin-bottom: 5px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .queue-label {
            font-size: 14px;
            color: #666;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Simple responsive design for appointment card */
        @media (max-width: 600px) {
            .appointment-confirmation-card,
            .patient-journey-card {
                margin: 10px 0;
            }
            
            .confirmation-header,
            .journey-header {
                padding: 15px;
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }
            
            .appointment-details,
            .journey-details {
                padding: 15px;
            }
            
            .detail-section {
                margin-bottom: 20px;
                padding-bottom: 15px;
            }
            
            .detail-item {
                margin: 10px 0;
                padding: 3px 0;
            }
            
            .detail-item strong {
                min-width: 80px;
                display: block;
                margin-bottom: 3px;
            }
            
            .orders-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }
            
            .important-note {
                flex-direction: column;
                text-align: center;
                gap: 8px;
            }

            .queue-number-large {
                font-size: 36px;
            }
        }


    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-left">
                <div class="logo">
                    <i class="fas fa-heartbeat"></i>
                </div>
                <div class="header-text">Chat with Athena</div>
            </div>
            <button class="close-button">&times;</button>
        </div>
        
        <div class="chat-container" id="chat-container">
            <div class="welcome-message">
                <h2>Hello there!</h2>
                <p>I'm <strong>Athena</strong>, your Smart Health Assistant!</p>
                <p>How can I <span class="highlight">help you today?</span></p>
                
                <div class="quick-actions">
                    <div class="action-button" onclick="sendQuickQuery('I have a health issue. What specialist should I see?')">
                        <i class="fas fa-stethoscope"></i> Ask Specialty
                    </div>
                    <div class="action-button" id="end-chat-btn" onclick="endChat()">
                        <i class="fas fa-trash-alt"></i> End Chat
                    </div>
                </div>
            </div>
            
            <div class="message-container">
            </div>
        </div>
        
        <div class="input-area">
            <div class="input-container">
                <input type="text" id="user-input" placeholder="Ask me anything...">
                <button class="send-button" onclick="sendMessage()">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
        
        <div class="debug-container">
            <div class="debug-toggle">
                <div class="toggle-wrapper">
                    <label class="toggle-switch">
                        <input type="checkbox" id="show-debug" onclick="toggleDebug()">
                        <span class="toggle-slider"></span>
                    </label>
                    <label for="show-debug">Debug mode</label>
                </div>
                <div class="connection-status" id="ws-status">
                    <div class="status-indicator status-disconnected"></div>
                    <span>Disconnected</span>
                </div>
            </div>
            <div class="debug-log" id="debug-log"></div>
        </div>
    </div>
    
    <script>
        // Session ID for memory persistence
        let sessionId = null;
        
        // Track if we're displaying specialty selection
        let specialtySelectionActive = false;
        
        // Initialize or retrieve session ID
        function initializeSessionId() {
            // Try to get session ID from localStorage
            const savedSessionId = localStorage.getItem('athena_session_id');
            
            if (savedSessionId) {
                sessionId = savedSessionId;
                console.log(`Restored session ID from storage: ${sessionId}`);
                addToDebugLog(`Using existing session ID: ${sessionId}`);
            } else {
                // Generate a new UUID
                sessionId = generateUUID();
                localStorage.setItem('athena_session_id', sessionId);
                console.log(`Generated new session ID: ${sessionId}`);
                addToDebugLog(`Created new session ID: ${sessionId}`);
            }
        }
        
        // Clear existing specialty selections
        function clearSpecialtySelections() {
            if (specialtySelectionActive) {
                // Don't remove the containers, just mark that we've handled them
                console.log("Specialty selection UI remains visible");
                
                // We're not actually clearing the UI, just updating the flag
                // This allows users to make multiple selections
                specialtySelectionActive = true;
            }
        }
        
        // Add specialty selection UI (enhanced version)
        function addSpecialtySelectionUI(messageContainer, options) {
            // Don't clear existing selections if they exist
            if (document.querySelector('.specialty-selection-container')) {
                console.log("Specialty selection UI already exists, not creating a new one");
                return;
            }
            
            // Create selection container
            const container = document.createElement('div');
            container.className = 'specialty-selection-container';
            
            // Add header
            const header = document.createElement('div');
            header.className = 'specialty-selection-header';
            header.innerHTML = '<i class="fas fa-stethoscope"></i> Medical Specialties';
            container.appendChild(header);
            
            // Add a note that explains multiple options when available
            const note = document.createElement('div');
            note.style.fontSize = '14px';
            note.style.color = '#666';
            note.style.marginBottom = '16px';
            note.style.lineHeight = '1.4';
            
            // Change the note text based on how many options are available
            if (options.length > 1) {
                note.innerHTML = '<i class="fas fa-info-circle" style="color: #4287f5; margin-right: 6px;"></i> Multiple specialties are recommended for your symptoms. Please select one to proceed.';
            } else {
                note.innerHTML = '<i class="fas fa-info-circle" style="color: #4287f5; margin-right: 6px;"></i> Please select this specialty to proceed.';
            }
            container.appendChild(note);
            
            // Log the options for debugging
            console.log(`Adding ${options.length} specialty options to UI:`, options);
            
            // Add options container
            const optionsContainer = document.createElement('div');
            optionsContainer.className = 'specialty-options';
            
            // Icons for different specialties (add more as needed)
            const specialtyIcons = {
                'GENERAL MEDICINE': 'fa-user-md',
                'CARDIOLOGY': 'fa-heartbeat',
                'DERMATOLOGY': 'fa-allergies',
                'NEUROLOGY': 'fa-brain',
                'ORTHOPEDICS': 'fa-bone',
                'PEDIATRICS': 'fa-child',
                'GYNECOLOGY': 'fa-female',
                'OPHTHALMOLOGY': 'fa-eye',
                'ENT': 'fa-ear',
                'PSYCHIATRY': 'fa-comment-medical',
                'UROLOGY': 'fa-kidneys',
                'GASTROENTEROLOGY': 'fa-stomach',
                'ENDOCRINOLOGY': 'fa-vial',
                'PULMONOLOGY': 'fa-lungs',
                'Accident & Emergency': 'fa-ambulance'
            };
            
            // Add option buttons
            options.forEach(option => {
                const button = document.createElement('button');
                button.className = 'specialty-option-button';
                
                // Choose appropriate icon or default to user-md
                const iconClass = specialtyIcons[option.label] || specialtyIcons[option.value] || 'fa-user-md';
                button.innerHTML = `<i class="fas ${iconClass}"></i> ${option.label}`;
                
                // Add click handler
                button.onclick = () => {
                    console.log(`Selected specialty: ${option.label} (${option.value})`);
                    
                    // Mark this button as selected visually
                    const allButtons = document.querySelectorAll('.specialty-option-button');
                    allButtons.forEach(btn => btn.classList.remove('selected'));
                    button.classList.add('selected');
                    
                    // Send selection to backend
                    sendSpecialtySelection(option.label, option.value);
                    
                    // Track that we have specialty selection active
                    specialtySelectionActive = true;
                };
                
                optionsContainer.appendChild(button);
            });
            
            container.appendChild(optionsContainer);
            messageContainer.appendChild(container);
            
            // Track that we have specialty selection active
            specialtySelectionActive = true;
        }
        
        // Generate UUID v4 for session identification
        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }
        
        // WebSocket connection for real-time logs
        let socket;
        let isConnected = false;
        
        // Chat history array to store messages
        let chatHistory = [];
        
        // Load chat history from localStorage if available
        function loadChatHistory() {
            const savedChat = localStorage.getItem('athena_chat_history');
            if (savedChat) {
                try {
                    chatHistory = JSON.parse(savedChat);
                    
                    // Display saved chat messages
                    chatHistory.forEach(message => {
                        addMessage(message.text, message.sender, false);
                    });
                    
                    // Scroll to bottom after loading history
                    const chatContainer = document.getElementById("chat-container");
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                } catch (error) {
                    console.error('Error loading chat history:', error);
                    // Reset chat history if there was an error
                    chatHistory = [];
                    localStorage.removeItem('athena_chat_history');
                }
            }
        }
        
        // Save chat history to localStorage
        function saveChatHistory() {
            try {
                localStorage.setItem('athena_chat_history', JSON.stringify(chatHistory));
            } catch (error) {
                console.error('Error saving chat history:', error);
            }
        }

        function connectWebSocket() {
            // Create WebSocket connection
            socket = new WebSocket(`ws://${window.location.host}/ws`);
            
            // Connection opened
            socket.addEventListener('open', function(event) {
                console.log('WebSocket connected!');
                isConnected = true;
                addToDebugLog("WebSocket connection established");
                
                // Update connection status indicator
                updateConnectionStatus(true);
            });
            
            // Listen for messages
            socket.addEventListener('message', function(event) {
                try {
                    const data = JSON.parse(event.data);
                    console.log('WebSocket message received:', data);
                    
                    // Remove typing indicator before adding agent message
                    removeTypingIndicator();
                    
                    if (data.error) {
                        addMessage(`Error: ${data.error}`, 'agent');
                        addToDebugLog(`WebSocket error: ${data.error}`);
                    } else {
                        // Update session ID if provided in response
                        if (data.session_id) {
                            sessionId = data.session_id;
                            localStorage.setItem('athena_session_id', sessionId);
                            console.log(`Updated session ID from response: ${sessionId}`);
                        }
                        
                        // Check if this is an appointment booking response
                        if (data.appointment_booked && data.appointment_details) {
                            console.log('✅ Received appointment booking confirmation:', data);
                            console.log('✅ Appointment details:', data.appointment_details);
                            console.log('✅ Visit ID:', data.visit_id);
                            console.log('✅ Appointment ID:', data.appointment_id);
                            // Display the appointment confirmation card
                            displayAppointmentConfirmation(data.appointment_details, data.visit_id, data.appointment_id);
                        }
                        // Check if this is a patient journey response
                        else if (data.patient_journey && data.journey_data) {
                            console.log('✅ Received patient journey data:', data);
                            console.log('✅ Journey data:', data.journey_data);
                            console.log('✅ Visit ID:', data.visit_id);
                            console.log('✅ Appointment ID:', data.appointment_id);
                            // Display the patient journey card
                            displayPatientJourney(data.journey_data, data.visit_id);
                        }
                        // Check if this is a session slots response
                        else if (data.slots_data && window.selectedAppointment) {
                            console.log('Received session slots data:', data.slots_data);
                            // Check if slots_data contains an error
                            if (data.slots_data.error) {
                                console.error('Error in slots data:', data.slots_data.error);
                                addMessage(`Sorry, I couldn't retrieve the available slots: ${data.slots_data.error}`, 'agent');
                            } else {
                                // Store the slot data globally for booking
                                window.currentSlotData = data.slots_data;
                                console.log('Stored slot data in window.currentSlotData:', window.currentSlotData);
                                
                                // Display the session slots instead of just the response message
                                displaySessionSlots(window.selectedAppointment, data.slots_data);
                            }
                        } else {
                            // Check if we have suggested actions in the response
                            const suggestedActions = data.suggested_actions || null;
                            
                            if (suggestedActions) {
                                console.log("📢 WebSocket response includes suggested actions:", JSON.stringify(suggestedActions));
                            }
                            
                            // Add agent response to chat
                            addMessage(data.response, 'agent', true, suggestedActions);
                        }
                    }
                } catch (error) {
                    console.error('Error parsing WebSocket message:', error);
                    removeTypingIndicator();
                    addMessage('Sorry, I encountered an error processing the response.', 'agent');
                    addToDebugLog(`WebSocket parse error: ${error.message}`);
                }
            });
            
            // Connection closed
            socket.addEventListener('close', function(event) {
                console.log('WebSocket disconnected');
                isConnected = false;
                addToDebugLog("WebSocket connection closed");
                
                // Update connection status indicator
                updateConnectionStatus(false);
                
                // Try to reconnect after a delay
                setTimeout(connectWebSocket, 3000);
            });
            
            // Connection error
            socket.addEventListener('error', function(event) {
                console.error('WebSocket error:', event);
                addToDebugLog("WebSocket error occurred");
                
                // Update connection status indicator
                updateConnectionStatus(false);
            });
        }
        
        // Update the connection status indicator
        function updateConnectionStatus(connected) {
            const statusDiv = document.getElementById('ws-status');
            const indicator = statusDiv.querySelector('.status-indicator');
            const statusText = statusDiv.querySelector('span');
            
            if (connected) {
                indicator.className = 'status-indicator status-connected';
                statusText.textContent = 'Connected';
            } else {
                indicator.className = 'status-indicator status-disconnected';
                statusText.textContent = 'Disconnected';
            }
        }
        
        // Simple emergency detection that runs on every message
        function checkForEmergency(text) {
            if (text && (text.includes('🚨') || text.toLowerCase().includes('urgent'))) {
                console.log('EMERGENCY DETECTED:', text);
                
                // Extract just the emergency advice part
                const emergencyMatch = text.match(/🚨[^]*?(?:emergency room|call emergency services|immediately)/i);
                let emergencyAdvice = '';
                
                if (emergencyMatch) {
                    emergencyAdvice = emergencyMatch[0];
                } else {
                    // Fallback to a standard emergency message
                    emergencyAdvice = "🚨 **URGENT**: This may require immediate medical attention. If symptoms are severe, call emergency services or go to the nearest emergency room.";
                }
                
                // Create a more user-friendly alert box
                const alertBox = document.createElement('div');
                alertBox.style.cssText = `
                    background: #fff5f5;
                    border: 2px solid #f56565;
                    border-radius: 12px;
                    margin: 15px 0;
                    padding: 0;
                    box-shadow: 0 4px 12px rgba(245, 101, 101, 0.15);
                    animation: pulse 2s ease-in-out infinite;
                `;
                
                alertBox.innerHTML = `
                    <div style="
                        background: #f56565;
                        color: white;
                        padding: 12px 20px;
                        border-radius: 10px 10px 0 0;
                        font-weight: bold;
                        font-size: 16px;
                    ">
                        🚨 MEDICAL EMERGENCY
                    </div>
                    <div style="
                        padding: 20px;
                        color: #333;
                        line-height: 1.5;
                    ">
                        <strong>This may require immediate medical attention.</strong><br>
                        If symptoms are severe, please seek immediate medical attention or call emergency services.
                    </div>
                `;
                
                // Add to top of chat
                const chatContainer = document.getElementById('chat-container');
                if (chatContainer) {
                    chatContainer.appendChild(alertBox);
                }
                
                return true;
            }
            return false;
        }

        // Connect WebSocket and load chat history on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Test console log to confirm JavaScript is working
            console.log('🚀 JavaScript is working! DOM loaded successfully.');
            
            // Add pulse animation style
            const style = document.createElement('style');
            style.textContent = `
                @keyframes pulse {
                    0% { box-shadow: 0 4px 12px rgba(245, 101, 101, 0.15); }
                    50% { box-shadow: 0 6px 20px rgba(245, 101, 101, 0.3); }
                    100% { box-shadow: 0 4px 12px rgba(245, 101, 101, 0.15); }
                }
            `;
            document.head.appendChild(style);
            
            // Initialize session ID first
            initializeSessionId();
            
            // Then connect WebSocket and load history
            connectWebSocket();
            loadChatHistory();
        });

        // Enable sending messages by hitting Enter
        document.getElementById("user-input").addEventListener("keyup", function(event) {
            if (event.key === "Enter") {
                sendMessage();
            }
        });
        
        // Function for quick action buttons
        function sendQuickQuery(query) {
            const userInput = document.getElementById("user-input");
            userInput.value = query;
            sendMessage();
        }
        
        // Show typing indicator
        function showTypingIndicator() {
            const chatContainer = document.getElementById("chat-container");
            
            // Check if typing indicator already exists
            if (document.getElementById("typing-indicator")) {
                return;
            }
            
            const typingIndicator = document.createElement("div");
            typingIndicator.className = "typing-indicator";
            typingIndicator.id = "typing-indicator";
            typingIndicator.innerHTML = `
                <span></span>
                <span></span>
                <span></span>
            `;
            chatContainer.appendChild(typingIndicator);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
        
        // Remove typing indicator
        function removeTypingIndicator() {
            const typingIndicator = document.getElementById("typing-indicator");
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }
        
        // Function to send specialty selection via button click
        function sendSpecialtySelection(specialtyName, specialtyId) {
            // Create a structured message for specialty selection
            const structuredMessage = {
                "message": "specialty_selected",
                "specialty_id": specialtyId,
                "specialty_name": specialtyName
            };
            
            // Convert to JSON string
            const jsonMessage = JSON.stringify(structuredMessage);
            
            // Add visual user feedback to show which specialty was selected
            addMessage(`Selected: ${specialtyName}`, 'user');
            
            // Mark the clicked button as selected
            const allButtons = document.querySelectorAll('.specialty-option-button');
            allButtons.forEach(button => {
                // Remove selected class from all buttons first
                button.classList.remove('selected');
                
                // Add selected class to the button that matches the selected specialty
                if (button.innerText.includes(specialtyName)) {
                    button.classList.add('selected');
                }
            });
            
            // Show typing indicator
            showTypingIndicator();
            
            console.log(`🏥 Sending specialty selection: ${specialtyName} (ID: ${specialtyId})`);
            console.log(`🏥 JSON message: ${jsonMessage}`);
            
            try {
                // Try WebSocket first if connected
                if (socket && socket.readyState === WebSocket.OPEN) {
                    // Send the message directly - no need for double JSON stringify
                    socket.send(JSON.stringify({ 
                        message: jsonMessage,
                        session_id: sessionId 
                    }));
                    addToDebugLog(`Specialty selection sent via WebSocket: ${specialtyName} (${specialtyId})`);
                } else {
                    // Fall back to REST API
                    addToDebugLog("WebSocket not available, using REST API for specialty selection");
                    
                    // Send message to API
                    fetch('/api/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ 
                            message: jsonMessage,
                            session_id: sessionId
                        })
                    })
                    .then(response => {
                        console.log("Response received:", response);
                        return response.json();
                    })
                    .then(data => {
                        // Remove typing indicator before adding agent message
                        removeTypingIndicator();
                        
                        console.log("API response data:", data);
                        
                        // Update session ID if provided in response
                        if (data.session_id) {
                            sessionId = data.session_id;
                            localStorage.setItem('athena_session_id', sessionId);
                            console.log(`Updated session ID from API: ${sessionId}`);
                        }
                        
                        if (data.error) {
                            addMessage("I'm sorry, I encountered an error. Please try again.", 'agent');
                            addToDebugLog(`Error: ${data.error}`);
                        } else {
                            // Add agent response to chat
                            addMessage(data.response, 'agent', true, data.suggested_actions);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        removeTypingIndicator();
                        addMessage('Sorry, I encountered an error processing your request.', 'agent');
                        addToDebugLog(`Error: ${error.message}`);
                    });
                }
            } catch (error) {
                console.error('Error:', error);
                removeTypingIndicator();
                addMessage('Sorry, I encountered an error processing your specialty selection.', 'agent');
                addToDebugLog(`Error: ${error.message}`);
            }
        }

        // Function to add messages to the chat container
        function addMessage(text, sender, saveToHistory = true, suggestedActions = null) {
            console.log('🎯 addMessage called with:');
            console.log('🎯 Text length:', text ? text.length : 'null');
            console.log('🎯 Sender:', sender);
            console.log('🎯 Text contains appointment-confirmation-card:', text ? text.includes('appointment-confirmation-card') : false);
            
            // FIRST: Check for emergency before doing anything else
            if (sender === 'agent') {
                console.log('Checking message for emergency:', text);
                checkForEmergency(text);
            }
            
            const chatContainer = document.getElementById("chat-container");
            
            // Create message container
            const messageContainer = document.createElement("div");
            messageContainer.className = "message-container";
            
            // Check if we have specialty selection actions
            let hasSpecialtySelection = false;
            let specialtyRecommendation = false;
            
            if (suggestedActions && Array.isArray(suggestedActions)) {
                suggestedActions.forEach(actionGroup => {
                    if (actionGroup.type === 'specialty_selection' && 
                        actionGroup.options && 
                        actionGroup.options.length > 0) {
                        hasSpecialtySelection = true;
                    }
                });
            }
            
            // Check if this is a specialty recommendation message
            if (sender === 'agent' && text && 
                ((typeof text === 'string' && 
                  (text.includes("recommend the following specialties") || 
                   text.includes("Please select a specialty"))) || 
                 hasSpecialtySelection)) {
                specialtyRecommendation = true;
            }
            
            // Create message element - only if it's not a specialty recommendation or it's a user message
            if (!specialtyRecommendation || sender === 'user') {
                const message = document.createElement("div");
                message.className = sender === 'user' ? "user-message message" : "agent-message message";
                
                // For specialty recommendations, simplify the message but preserve emergency advice
                let displayText = text;
                if (specialtyRecommendation && sender === 'agent') {
                    // Check if there's emergency advice in the message
                    const hasEmergencyAdvice = text.includes('🚨') || text.includes('⚠️') || text.toLowerCase().includes('urgent');
                    
                    if (hasEmergencyAdvice) {
                        // Keep the full message to preserve emergency advice
                        displayText = text;
                    } else {
                        // Extract just the first part before the bullet points
                        const firstSentence = text.split('•')[0].trim();
                        displayText = firstSentence || "Please select a specialty:";
                    }
                }
                
                // Check if this is a doctor availability message and format it nicely
                if (sender === 'agent' && text.includes("Here are the available") && text.includes("doctors")) {
                    // Format doctor information in a structured way
                    const formattedHtml = formatDoctorAvailability(text);
                    message.innerHTML = formattedHtml;
                } 
                // Check if this is a specialty listing message and format it nicely
                else if (sender === 'agent' && (text.includes("Here are the medical specialties") || text.includes("specialties available"))) {
                    // Format specialty listing in a structured way
                    const formattedHtml = formatSpecialtyListing(text);
                    message.innerHTML = formattedHtml;
                }
                else {
                    // Handle markdown-like formatting for bold text
                    let formattedText = displayText.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
                    
                    // ALWAYS check the original full text for emergency advice, regardless of specialty recommendation
                    let textToCheck = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
                    
                    console.log('=== EMERGENCY DETECTION DEBUG ===');
                    console.log('Original text:', text);
                    console.log('Display text:', displayText);
                    console.log('Formatted text:', formattedText);
                    console.log('Text to check:', textToCheck);
                    console.log('Contains 🚨:', textToCheck.includes('🚨'));
                    console.log('Contains ⚠️:', textToCheck.includes('⚠️'));
                    console.log('Contains urgent:', textToCheck.toLowerCase().includes('urgent'));
                    console.log('Is specialty recommendation:', specialtyRecommendation);
                    
                    // Simple emergency detection - check if text contains emergency indicators
                    if (textToCheck.includes('🚨') || textToCheck.includes('⚠️') || 
                        textToCheck.toLowerCase().includes('urgent')) {
                        console.log('🚨 EMERGENCY ADVICE DETECTED! Creating alert...');
                        
                        // For testing, just show a simple alert first
                        alert('Emergency text detected: ' + textToCheck);
                        
                        // Find emergency text
                        const emergencyRegex = /🚨[^]*?(?:emergency room|call emergency services|immediately)/i;
                        const emergencyMatch = textToCheck.match(emergencyRegex);
                        
                        if (emergencyMatch) {
                            console.log('Emergency advice found:', emergencyMatch[0]);
                            
                            // Display regular message (remove emergency part)
                            const regularText = textToCheck.replace(emergencyMatch[0], '').trim();
                            message.innerHTML = regularText;
                            
                            // Create emergency alert
                            createEmergencyAlert(emergencyMatch[0], messageContainer);
                        } else {
                            // If no match, just highlight the whole message
                            message.innerHTML = formattedText;
                            console.log('No emergency pattern match, showing regular message');
                        }
                    } else {
                        // No emergency advice, regular formatting
                        message.innerHTML = formattedText;
                        console.log('No emergency advice detected, using regular formatting');
                    }
                }
                
                // Add timestamp to message
                const timestamp = document.createElement("div");
                timestamp.className = "message-timestamp";
                const now = new Date();
                // Format date as MM/DD/YYYY HH:MM for a cleaner look
                const formattedDate = now.toLocaleDateString([], {month: 'numeric', day: 'numeric', year: 'numeric'});
                const formattedTime = now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                timestamp.textContent = formattedDate + " · " + formattedTime;
                message.appendChild(timestamp);
                
                messageContainer.appendChild(message);
            }
            
            // Log suggestedActions to console only (not visible in UI)
            if (suggestedActions) {
                console.log("Received suggestedActions:", JSON.stringify(suggestedActions));
            }
            
            // Add suggested actions if available and not a user message
            if (sender !== 'user' && suggestedActions && Array.isArray(suggestedActions)) {
                console.log(`Processing ${suggestedActions.length} action groups`);
                
                // Process each action group
                for (const actionGroup of suggestedActions) {
                    console.log(`Action group type: ${actionGroup.type}`);
                    console.log(`Action group data:`, actionGroup);
                    
                    if (actionGroup.type === 'specialty_selection' && actionGroup.options && actionGroup.options.length > 0) {
                        console.log(`Found ${actionGroup.options.length} specialty options to display`);
                        
                        // Only add specialty selection UI if not already present
                        if (!document.querySelector('.specialty-selection-container')) {
                            // Use the new enhanced UI for specialty selections
                            addSpecialtySelectionUI(messageContainer, actionGroup.options);
                            console.log(`Added ${actionGroup.options.length} specialty options with enhanced UI`);
                        }
                    }
                }
            }
            
            console.log('🎯 About to append messageContainer to chatContainer');
            console.log('🎯 MessageContainer HTML length:', messageContainer.innerHTML.length);
            chatContainer.appendChild(messageContainer);
            console.log('🎯 MessageContainer successfully appended to chat');
            
            // Scroll to bottom
            chatContainer.scrollTop = chatContainer.scrollHeight;
            
            // Add to chat history and save to localStorage
            if (saveToHistory) {
                chatHistory.push({
                    text: text,
                    sender: sender,
                    timestamp: Date.now()
                    // Don't save suggested actions to local storage
                });
                
                saveChatHistory();
            }
        }
        
        // Function to create emergency alert box
        function createEmergencyAlert(emergencyAdvice, messageContainer) {
            console.log('🚨 createEmergencyAlert called with:', emergencyAdvice);
            console.log('messageContainer:', messageContainer);
            
            // Also show an alert to confirm this function is being called
            alert('Emergency alert function called! Emergency advice: ' + emergencyAdvice);
            
            const emergencyDiv = document.createElement('div');
            emergencyDiv.className = 'emergency-advice emergency-alert';
            emergencyDiv.style.cssText = `
                background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
                border: 3px solid #f44336;
                border-radius: 15px;
                padding: 20px;
                margin: 20px 0;
                box-shadow: 0 6px 20px rgba(244, 67, 54, 0.3);
                animation: emergencyPulse 2s ease-in-out infinite;
                position: relative;
                overflow: hidden;
            `;
            
            // Add CSS animation for emergency pulse
            if (!document.getElementById('emergency-styles')) {
                const style = document.createElement('style');
                style.id = 'emergency-styles';
                style.textContent = `
                    @keyframes emergencyPulse {
                        0% { box-shadow: 0 6px 20px rgba(244, 67, 54, 0.3); }
                        50% { box-shadow: 0 8px 25px rgba(244, 67, 54, 0.5); }
                        100% { box-shadow: 0 6px 20px rgba(244, 67, 54, 0.3); }
                    }
                    
                    @keyframes shake {
                        0%, 100% { transform: translateX(0); }
                        25% { transform: translateX(-2px); }
                        75% { transform: translateX(2px); }
                    }
                    
                    .emergency-alert::before {
                        content: '';
                        position: absolute;
                        top: -50%;
                        left: -50%;
                        width: 200%;
                        height: 200%;
                        background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
                        animation: shimmer 3s infinite;
                    }
                    
                    @keyframes shimmer {
                        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
                        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
                    }
                    
                    .inline-emergency {
                        background: #ffebee;
                        border: 2px solid #f44336;
                        border-radius: 8px;
                        padding: 10px;
                        margin: 10px 0;
                        color: #c62828;
                        font-weight: 600;
                        animation: emergencyPulse 2s ease-in-out infinite;
                    }
                `;
                document.head.appendChild(style);
            }
            
            emergencyDiv.innerHTML = `
                <div style="display: flex; align-items: flex-start; gap: 15px; position: relative; z-index: 1;">
                    <div style="flex-shrink: 0;">
                        <i class="fas fa-exclamation-triangle" style="color: #f44336; font-size: 28px; animation: shake 0.8s ease-in-out infinite alternate;"></i>
                    </div>
                    <div style="flex: 1;">
                        <div style="font-weight: bold; color: #c62828; font-size: 18px; margin-bottom: 10px; text-transform: uppercase; letter-spacing: 0.5px;">
                            🚨 MEDICAL EMERGENCY ALERT 🚨
                        </div>
                        <div style="color: #d32f2f; font-size: 16px; line-height: 1.6; font-weight: 500;">
                            ${emergencyAdvice}
                        </div>
                        <div style="margin-top: 15px; padding: 10px; background: rgba(244, 67, 54, 0.1); border-radius: 8px; border-left: 4px solid #f44336;">
                            <div style="font-size: 14px; color: #c62828; font-weight: 600;">
                                📞 Emergency Services: <span style="font-size: 16px; color: #b71c1c;">911</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Add the emergency advice after the regular message
            messageContainer.appendChild(emergencyDiv);
        }
        
        // Function to format doctor availability information
        function formatDoctorAvailability(text) {
            // Extract specialty name - more flexible pattern to handle various specialty names
            const specialtyMatch = text.match(/Here are the available ([A-Z\s&,'-]+) doctors/);
            const specialty = specialtyMatch ? specialtyMatch[1] : "SPECIALTY";
            
            // Start building HTML
            let html = `<p>Here are the available <strong>${specialty}</strong> doctors:</p>`;
            
            // New pattern to match the updated format where each session is separate
            // Pattern: • **Dr. NAME** \n  TIME \n  X slots available \n  RESOURCEID: X \n  SESSIONID: X \n  SESSIONDATE: X
            const sessionPattern = /• \*\*Dr\. ([^*]+)\*\*\s*\n\s*(\d{2}:\d{2})\s*\n\s*(\d+) slots available\s*\n\s*RESOURCEID: (\d+)\s*\n\s*SESSIONID: (\d+)\s*\n\s*SESSIONDATE: ([^\n]+)/g;
            let sessionMatch;
            
            // Store sessions grouped by doctor for better display
            const doctorSessions = {};
            
            // Extract all sessions
            while ((sessionMatch = sessionPattern.exec(text)) !== null) {
                const doctorName = sessionMatch[1].trim();
                const sessionTime = sessionMatch[2];
                const availableSlots = parseInt(sessionMatch[3]);
                const resourceId = sessionMatch[4];
                const sessionId = sessionMatch[5];
                const sessionDate = sessionMatch[6].trim();
                
                if (!doctorSessions[doctorName]) {
                    doctorSessions[doctorName] = [];
                }
                
                doctorSessions[doctorName].push({
                    time: sessionTime,
                    slots: availableSlots,
                    resourceId: resourceId,
                    sessionId: sessionId,
                    sessionDate: sessionDate
                });
            }
            
            // If no sessions found with the new pattern, try the old pattern for backward compatibility
            if (Object.keys(doctorSessions).length === 0) {
                const oldPattern = /• Dr ([^:]+):([\s\S]*?)(?=• Dr|Please select|$)/g;
                let oldMatch;
                
                while ((oldMatch = oldPattern.exec(text)) !== null) {
                    const doctorName = oldMatch[1].trim();
                    const sessionsText = oldMatch[2].trim();
                    
                    // Extract time and slots from the sessions text
                    const timeMatch = sessionsText.match(/(\d{2}:\d{2})/);
                    const slotsMatch = sessionsText.match(/(\d+) slots available/);
                    
                    if (timeMatch && slotsMatch) {
                        if (!doctorSessions[doctorName]) {
                            doctorSessions[doctorName] = [];
                        }
                        
                        doctorSessions[doctorName].push({
                            time: timeMatch[1],
                            slots: parseInt(slotsMatch[1])
                        });
                    }
                }
            }
            
            // Now create the HTML for each doctor
            for (const [doctorName, sessions] of Object.entries(doctorSessions)) {
                // Calculate total slots for this doctor
                const totalSlots = sessions.reduce((sum, session) => sum + session.slots, 0);
                
                // Get the earliest session time for display
                const earliestTime = sessions.reduce((earliest, session) => {
                    return session.time < earliest ? session.time : earliest;
                }, sessions[0].time);
                
                // Create doctor card
                html += `<div class="doctor-card">
                    <div class="doctor-name"><i class="fas fa-user-md"></i> Dr. ${doctorName}</div>
                    <div class="session-list">`;
                
                // Now create individual session entries instead of grouping by doctor
                sessions.forEach((session, index) => {
                    const sessionId = `session-${doctorName.replace(/\s+/g, '-')}-${index}`;
                    
                    // Create appointment data for this specific session
                    const appointmentData = JSON.stringify({
                        doctor: doctorName,
                        resource_id: session.resourceId,
                        session_date: session.sessionDate,
                        session_id: session.sessionId,
                        specialty: specialty
                    }).replace(/"/g, '&quot;');
                    
                    // Display each session separately
                    html += `<div class="session-item" id="${sessionId}" onclick="getDetailedSlots('${sessionId}', '${appointmentData}')">
                        <div class="session-info">
                            <span class="session-time"><i class="fas fa-clock"></i> ${session.time}</span>
                            <span class="session-slots"><i class="fas fa-calendar-check"></i> ${session.slots} slots available</span>
                        </div>
                    </div>`;
                });
                
                html += `</div></div>`;
            }
            
            // Add appointment prompt
            html += `<p class="appointment-prompt">Please select a doctor to see available slots.</p>`;
            
            return html;
        }
        
        // Function to get detailed slots for a session
        function getDetailedSlots(doctorId, appointmentDataStr) {
            try {
                // Parse the appointment data
                const appointmentData = JSON.parse(appointmentDataStr.replace(/&quot;/g, '"'));
                
                console.log(`Getting detailed slots for doctor: ${appointmentData.doctor}`);
                
                // First, remove selected class from all session items
                document.querySelectorAll('.session-item').forEach(item => {
                    item.classList.remove('selected');
                });
                
                // Add selected class to clicked session
                const selectedSession = document.getElementById(doctorId);
                if (selectedSession) {
                    selectedSession.classList.add('selected');
                    
                    // Store selected appointment data
                    window.selectedAppointment = appointmentData;
                    
                    // Call the API to get detailed slots
                    fetchSessionSlots(appointmentData);
                }
            } catch (error) {
                console.error('Error parsing appointment data:', error);
            }
        }
        
        // Function to fetch session slots from the API
        async function fetchSessionSlots(appointmentData) {
            try {
                // Ensure session_date is in ISO format for API calls
                const sessionDateObj = new Date(appointmentData.session_date);
                const sessionDateISO = sessionDateObj.toISOString();
                
                const requestData = {
                    message: "get_session_slots",
                    resource_id: appointmentData.resource_id,
                    session_date: sessionDateISO, // Use ISO format for API call
                    session_id: appointmentData.session_id
                };
                
                // Send the request via WebSocket for better real-time experience
                if (socket && socket.readyState === WebSocket.OPEN) {
                    socket.send(JSON.stringify({
                        message: JSON.stringify(requestData),
                        session_id: sessionId
                    }));
                    console.log(`Sent session slots request via WebSocket for resource ID: ${appointmentData.resource_id}`);
                } else {
                    // Fallback to REST API if WebSocket not available
                    const response = await fetch('/api/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            message: JSON.stringify(requestData),
                            session_id: sessionId
                        })
                    });
                    
                    if (!response.ok) {
                        throw new Error('Failed to fetch session slots');
                    }
                    
                    const data = await response.json();
                    console.log('Session slots response:', data);
                    
                    // Display the slots
                    displaySessionSlots(appointmentData, data);
                }
            } catch (error) {
                console.error('Error fetching session slots:', error);
                addMessage(`Sorry, I couldn't retrieve the available slots. Please try again.`, 'agent');
            }
        }
        
        // Function to display session slots
        function displaySessionSlots(appointmentData, slotsData) {
            const chatContainer = document.getElementById("chat-container");
            
            // Create a message container
            const messageContainer = document.createElement("div");
            messageContainer.className = "message-container";
            
            // Create the agent message element
            const message = document.createElement("div");
            message.className = "agent-message message";
            
            // Create the slots HTML
            let slotsHtml = `<div class="slot-header">
                <div class="doctor-info">
                    <div class="doctor-name"><i class="fas fa-user-md"></i> Dr. ${appointmentData.doctor}</div>
                    <div class="session-date">Date: ${appointmentData.session_date}</div>
                </div>
            </div>`;
            
            slotsHtml += `<p>Available Slots</p>`;
            
            // Check if we have slots data - handle both direct slots and slots_data.slots structure
            let slots = [];
            if (slotsData.slots_data && slotsData.slots_data.slots) {
                slots = slotsData.slots_data.slots;
            } else if (slotsData.slots) {
                slots = slotsData.slots;
            }
            
            console.log("=== SIMPLIFIED SLOTS DATA ===");
            console.log("Raw slotsData received:", slotsData);
            console.log("Parsed slots array:", slots);
            console.log("Slots length:", slots.length);
            
            if (slots && slots.length > 0) {
                console.log(`✅ Found ${slots.length} slots to process`);
                slotsHtml += `<div class="slots-container">`;
                
                // Get current time in Singapore timezone for proper comparison
                const now = new Date();
                const singaporeNow = new Date(now.toLocaleString("en-US", {timeZone: "Asia/Singapore"}));
                
                console.log(`Current time (local): ${now.toISOString()}`);
                console.log(`Current time (Singapore): ${singaporeNow.toISOString()}`);
                
                // For future dates, show all slots (don't filter by current time)
                const requestedDate = new Date(appointmentData.session_date);
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                requestedDate.setHours(0, 0, 0, 0);
                
                const isToday = requestedDate.getTime() === today.getTime();
                console.log(`Is today: ${isToday}, Requested date: ${requestedDate.toISOString()}, Today: ${today.toISOString()}`);
                
                // Filter slots based on date and time
                const availableSlots = slots.filter(slot => {
                    // Server now sends Singapore timezone data, create Date object directly
                    let slotDateTime = null;
                    
                    if (slot.SLOTTIME) {
                        slotDateTime = new Date(slot.SLOTTIME);
                    } else if (slot.STARTTIME) {
                        slotDateTime = new Date(slot.STARTTIME);
                    }
                    
                    if (!slotDateTime) {
                        console.log(`Skipping slot - no valid time found:`, slot);
                        return false;
                    }
                    
                    // If it's today, only show future slots. If it's a future date, show all slots.
                    let shouldShow = true;
                    if (isToday) {
                        // Convert slot time to Singapore timezone for comparison
                        const slotSingaporeTime = new Date(slotDateTime.toLocaleString("en-US", {timeZone: "Asia/Singapore"}));
                        shouldShow = slotSingaporeTime > singaporeNow;
                        console.log(`Today slot check - Slot: ${slotSingaporeTime.toISOString()}, Now: ${singaporeNow.toISOString()}, Show: ${shouldShow}`);
                    } else {
                        console.log(`Future date slot - showing all slots`);
                    }
                    
                    return shouldShow;
                });
                
                console.log(`=== SLOT FILTERING RESULTS ===`);
                console.log(`Total slots: ${slots.length}, Available slots: ${availableSlots.length}`);
                
                // Process and display each available slot
                availableSlots.forEach((slot, index) => {
                    console.log(`Processing slot ${index}:`, slot);
                    
                    // Extract time from slot - server now sends Singapore timezone
                    let slotDateTime = null;
                    
                    if (slot.SLOTTIME) {
                        slotDateTime = new Date(slot.SLOTTIME);
                        console.log(`Using SLOTTIME: ${slot.SLOTTIME} -> Date object: ${slotDateTime}`);
                    } else if (slot.STARTTIME) {
                        slotDateTime = new Date(slot.STARTTIME);
                        console.log(`Using STARTTIME: ${slot.STARTTIME} -> Date object: ${slotDateTime}`);
                    }
                    
                    if (!slotDateTime) {
                        console.log(`Skipping slot ${index} - no valid time found`);
                        return;
                    }
                    
                    // Format time for storage (ISO format for POST requests)
                    const slotTimeISO = slotDateTime.toISOString();
                    
                    // Format time for display - parse Singapore time directly
                    // Server sends time like "2025-07-05T09:30:00+08:00"
                    let formattedTime;
                    if (slot.SLOTTIME && slot.SLOTTIME.includes('+08:00')) {
                        // Extract time portion from Singapore timezone string
                        const timeMatch = slot.SLOTTIME.match(/T(\d{2}):(\d{2}):\d{2}\+08:00/);
                        if (timeMatch) {
                            const hours = parseInt(timeMatch[1]);
                            const minutes = timeMatch[2];
                            const period = hours >= 12 ? 'PM' : 'AM';
                            const displayHours = hours > 12 ? hours - 12 : (hours === 0 ? 12 : hours);
                            formattedTime = `${displayHours.toString().padStart(2, '0')}:${minutes} ${period}`;
                        } else {
                            // Fallback to Date formatting
                            formattedTime = slotDateTime.toLocaleTimeString('en-US', {
                                hour: '2-digit',
                                minute: '2-digit',
                                hour12: true,
                                timeZone: 'Asia/Singapore'
                            });
                        }
                    } else {
                        // Fallback to Date formatting
                        formattedTime = slotDateTime.toLocaleTimeString('en-US', {
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: true,
                            timeZone: 'Asia/Singapore'
                        });
                    }
                    
                    console.log(`Slot ${index}: Original=${slot.SLOTTIME}, Date=${slotDateTime}, Formatted=${formattedTime}`);
                    
                    // Create a unique slot ID
                    const slotId = `slot-${index}`;
                    
                    // Check if the slot is booked - API returns "Y" for booked, "N" for available
                    const isBooked = slot.BOOKED === "Y" || slot.BOOKED === "y";
                    const slotClass = isBooked ? "time-slot booked" : "time-slot available";
                    const slotLabel = isBooked ? `${formattedTime} (Booked)` : formattedTime;
                    
                    console.log(`Slot ${index}: ${formattedTime}, booked: ${isBooked} (BOOKED field: ${slot.BOOKED})`);
                    
                    // Add the slot to the UI
                    slotsHtml += `
                    <div class="${slotClass}" id="${slotId}" ${!isBooked ? `onclick="selectTimeSlot('${slotId}', '${slotTimeISO}', '${formattedTime}')"` : ''}>
                        ${slotLabel}
                    </div>`;
                });
                
                slotsHtml += `</div>`;
                
                // Show message if no available slots
                if (availableSlots.length === 0) {
                    slotsHtml += `<p>No available slots remaining.</p>`;
                } else {
                    // Initialize the appointment selection data
                    window.selectedAppointment = {
                        resource_id: appointmentData.resource_id,
                        session_id: appointmentData.session_id,
                        session_date: appointmentData.session_date,
                        doctor: appointmentData.doctor,
                        specialty: appointmentData.specialty,
                        slotTimeISO: null,
                        formattedTime: null
                    };
                    
                    // Add a book appointment button
                    slotsHtml += `
                    <button class="book-button" id="book-appointment-btn" onclick="bookSelectedAppointment()" disabled>
                        <i class="fas fa-calendar-plus"></i> Book Appointment
                    </button>`;
                }
            } else {
                console.log(`❌ No slots found`);
                slotsHtml += `<p>No available slots found.</p>`;
            }
            
            // Set the HTML
            message.innerHTML = slotsHtml;
            
            // Add timestamp to message
            const timestamp = document.createElement("div");
            timestamp.className = "message-timestamp";
            const now = new Date();
            const formattedDate = now.toLocaleDateString([], {month: 'numeric', day: 'numeric', year: 'numeric'});
            const formattedTime = now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            timestamp.textContent = formattedDate + " · " + formattedTime;
            message.appendChild(timestamp);
            
            // Add the message to the container
            messageContainer.appendChild(message);
            
            // Add the container to the chat
            chatContainer.appendChild(messageContainer);
            
            // Scroll to the new message
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
        
        // Function to select a time slot - directly books the appointment
        function selectTimeSlot(slotId, slotTimeISO, formattedTime) {
            // Remove selected class from all time slots
            document.querySelectorAll('.time-slot').forEach(item => {
                item.classList.remove('selected');
            });
            
            // Add selected class to clicked slot
            const selectedSlot = document.getElementById(slotId);
            if (selectedSlot) {
                selectedSlot.classList.add('selected');
                
                // Booking will be processed immediately
                
                // Get the appointment data from the selected slot
                if (window.selectedAppointment && window.currentSlotData) {
                    // Find the exact slot data that matches the clicked slot
                    const exactSlot = window.currentSlotData.slots.find(slot => {
                        const slotTimeLocal = new Date(slot.SLOTTIME).toISOString();
                        return slotTimeLocal === slotTimeISO;
                    });
                    
                    if (!exactSlot) {
                        console.error('Could not find matching slot data');
                        addMessage("Sorry, I couldn't find the slot information. Please try again.", 'agent');
                        return;
                    }
                    
                    // Extract the original UTC time in HH:MM:SS format for the API call
                    console.log('Exact slot data:', exactSlot);
                    console.log('SLOTTIME_ORIGINAL:', exactSlot.SLOTTIME_ORIGINAL);
                    console.log('SLOTTIME:', exactSlot.SLOTTIME);
                    
                    const originalSlotTimeString = exactSlot.SLOTTIME_ORIGINAL;
                    const fromTime = originalSlotTimeString.split('T')[1]; // Get the UTC time part after 'T'
                    
                    console.log(`Original slot time: ${originalSlotTimeString}, extracted time: ${fromTime}`);
                    
                    // Create booking message - patient_id will be handled by backend, individual_id will be visit_id from CreateWalkin response
                    const bookingMessage = {
                        message: "book_slot",
                        resource_id: window.selectedAppointment.resource_id,
                        session_date: window.selectedAppointment.session_date,
                        session_id: window.selectedAppointment.session_id,
                        from_time: fromTime
                        // Note: patient_id and individual_id will be handled by backend
                    };
                    
                    console.log('Booking appointment with data:', bookingMessage);
                    
                    // Send booking request via WebSocket
                    if (socket && socket.readyState === WebSocket.OPEN) {
                        socket.send(JSON.stringify({ 
                            message: JSON.stringify(bookingMessage),
                            session_id: sessionId 
                        }));
                        console.log('Appointment booking sent via WebSocket');
                    } else {
                        console.error('WebSocket not available for booking');
                        addMessage("Sorry, I couldn't book your appointment. Please try again.", 'agent');
                    }
                } else {
                    console.error('Missing appointment or slot data');
                    addMessage("Sorry, I couldn't find the appointment information. Please try again.", 'agent');
                }
            }
        }


        // Initialize the appointment selection variable
        window.selectedAppointment = null;
        
        // Add function to handle appointment slot selection
        function selectAppointmentSlot(sessionId, appointmentDataStr) {
            console.log(`Selecting appointment slot: ${sessionId}`);
            console.log(`Appointment data string: ${appointmentDataStr}`);
            
            try {
                // Parse the appointment data
                const appointmentData = JSON.parse(appointmentDataStr.replace(/&quot;/g, '"'));
                
                // First, remove selected class from all session items
                document.querySelectorAll('.session-item').forEach(item => {
                    item.classList.remove('selected');
                });
                
                // Add selected class to clicked session
                const selectedSession = document.getElementById(sessionId);
                if (selectedSession) {
                    selectedSession.classList.add('selected');
                    
                    // Enable booking button
                    const bookButton = document.getElementById('book-appointment-btn');
                    if (bookButton) {
                        bookButton.removeAttribute('disabled');
                        bookButton.classList.add('active');
                    }
                    
                    // Store selected appointment data
                    window.selectedAppointment = appointmentData;
                    
                    console.log(`Selected appointment: ${JSON.stringify(appointmentData)}`);
                }
            } catch (error) {
                console.error('Error parsing appointment data:', error);
            }
        }
        
        // Removed old form-based booking - now handled directly in selectTimeSlot
        
        // Function to submit patient information
        function submitPatientInfo() {
            const patientName = document.getElementById('patient-name').value.trim();
            const patientId = document.getElementById('patient-id').value.trim();
            const patientPhone = document.getElementById('patient-phone').value.trim();
            const patientEmail = document.getElementById('patient-email').value.trim();
            const patientNotes = document.getElementById('patient-notes').value.trim();
            
            // Validate required fields
            if (!patientName) {
                alert('Please enter your full name');
                return;
            }
            
            if (!patientPhone) {
                alert('Please enter your phone number');
                return;
            }
            
            // Get the appointment data
            const appointment = window.currentBookingAppointment;
            
            if (!appointment) {
                console.error('No appointment data found');
                return;
            }
            
            // Create a message with all the booking details
            const bookingMessage = `I would like to confirm my appointment with Dr. ${appointment.doctor} on ${appointment.date} (${appointment.time}) for ${appointment.specialty}. 
Patient details:
- Name: ${patientName}
- ID: ${patientId}
- Phone: ${patientPhone}
- Email: ${patientEmail}
- Notes: ${patientNotes}`;
            
            // Send the booking message
            sendQuickQuery(bookingMessage);
            
            // Clear the current booking
            window.currentBookingAppointment = null;
            
            console.log(`Submitted booking with patient info: ${patientName}`);
        }

        // Function to format specialty listings
        function formatSpecialtyListing(text) {
            // Check if this is a specialty listing message
            if (!text.includes("Here are the medical specialties") && !text.includes("specialties available")) {
                return text;
            }
            
            console.log("Formatting specialty listing:", text);
            
            // Start building HTML
            let html = `<p>Here are the medical specialties available at our hospital:</p>`;
            
            // Add helpful note
            html += `<p style="color: #666; font-style: italic; margin: 10px 0;">
                <i class="fas fa-info-circle" style="color: var(--primary-color);"></i> 
                Click on any specialty to inquire about it.
            </p>`;
            
            // Define specialty categories with proper organization
            const categories = {
                "General Medicine": {
                    icon: "fa-user-md",
                    specialties: []
                },
                "Cardiovascular": {
                    icon: "fa-heartbeat",
                    specialties: []
                },
                "Surgical": {
                    icon: "fa-procedures",
                    specialties: []
                },
                "Neurological": {
                    icon: "fa-brain",
                    specialties: []
                },
                "Digestive & Internal": {
                    icon: "fa-stomach",
                    specialties: []
                },
                "Sensory Organs": {
                    icon: "fa-eye",
                    specialties: []
                },
                "Women's Health": {
                    icon: "fa-female",
                    specialties: []
                },
                "Rehabilitation": {
                    icon: "fa-hand-holding-medical",
                    specialties: []
                },
                "Other Specialties": {
                    icon: "fa-stethoscope",
                    specialties: []
                }
            };
            
            // Define accurate mapping of specialties to categories based on API data
            const specialtyMapping = {
                // General Medicine
                "GENERAL MEDICINE": "General Medicine",
                "GERIATRIC MEDICINE": "General Medicine",
                "MIXED- MED,ORTH, SUR": "General Medicine",
                "MED": "General Medicine",
                "DAY HOSPITAL": "General Medicine",
                "GEM": "General Medicine",
                "PHC": "General Medicine",
                
                // Cardiovascular
                "CARDIOLOGY": "Cardiovascular",
                "CV": "Cardiovascular",
                "CTS": "Cardiovascular",
                "CTR": "Cardiovascular",
                "VAS": "Cardiovascular",
                "ANAESTHESIA": "Cardiovascular",
                "Accident & Emergency": "Cardiovascular",
                
                // Surgical
                "GENERAL SURGERY": "Surgical",
                "ORTHOPAEDIC SURGERY": "Surgical",
                "ORAL AND MAXILLOFACIAL SURGERY": "Surgical",
                "NEUROSURGERY": "Surgical",
                "SUR": "Surgical",
                "OPS": "Surgical",
                "BRE": "Surgical",
                "HS": "Surgical",
                
                // Neurological
                "NEUROLOGY": "Neurological",
                "NUEROLOGY": "Neurological",
                "NEUROPSYCHIATRY": "Neurological",
                "PSYCHOLOGICAL MEDICINE": "Neurological",
                "NEO": "Neurological",
                
                // Digestive & Internal
                "GASTROENTEROLOGY": "Digestive & Internal",
                "GAS": "Digestive & Internal",
                "DIETETIC CONSULTATION": "Digestive & Internal",
                "END": "Digestive & Internal",
                "REN": "Digestive & Internal",
                "HEPATOLOGY": "Digestive & Internal",
                "TOX": "Digestive & Internal",
                "RES": "Digestive & Internal",
                
                // Sensory Organs
                "EYE": "Sensory Organs",
                "EAR, NOSE & THROAT": "Sensory Organs",
                "OPHTHALMOLOGY": "Sensory Organs",
                
                // Women's Health
                "OBSTERICS AND GYNAECOLOGY": "Women's Health",
                "UROLOGY": "Women's Health",
                
                // Rehabilitation
                "REHABILITATION": "Rehabilitation",
                "REHABILITATION MEDICINE": "Rehabilitation",
                "PHYSIOTHERAPY": "Rehabilitation",
                "OCCUPATIONAL THERAPY": "Rehabilitation",
                "SPEECH THERAPY": "Rehabilitation",
                "RHI": "Rehabilitation",
                "RHP": "Rehabilitation",
                "PALLIATIVE CARE": "Rehabilitation",
                
                // Other Specialties (default category)
                "DERMATOLOGY": "Other Specialties",
                "DENTISTRY": "Other Specialties",
                "RADIOLOGY/X-RAY": "Other Specialties",
                "PODIATRY": "Other Specialties",
                "FGD": "Other Specialties",
                "MSS": "Other Specialties",
                "XRAY": "Other Specialties",
                "TC": "Other Specialties",
                "SPO": "Other Specialties"
            };
            
            // Define exact specialty IDs from API data
            const specialtyIds = {
                "Accident & Emergency": "1",
                "ANAESTHESIA": "27",
                "BRE": "4",
                "CARDIOLOGY": "20",
                "CMS": "48",
                "CTR": "56",
                "CTS": "6",
                "CV": "63",
                "DAY HOSPITAL": "35",
                "DENTISTRY": "7",
                "DERMATOLOGY": "44",
                "DIETETIC CONSULTATION": "30",
                "EAR, NOSE & THROAT": "8",
                "END": "50",
                "EYE": "9",
                "FGD": "45",
                "GAS": "3",
                "GASTROENTEROLOGY": "42",
                "GEM": "54",
                "GENERAL MEDICINE": "11",
                "GENERAL SURGERY": "18",
                "GERIATRIC MEDICINE": "10",
                "HA TEST": "61",
                "HS": "60",
                "MED": "24",
                "MIXED- MED,ORTH, SUR": "12",
                "MSS": "43",
                "NEO": "53",
                "NEUROLOGY": "38",
                "NEUROSURGERY": "37",
                "NUEROLOGY": "31",
                "OBSTERICS AND GYNAECOLOGY": "40",
                "OCCUPATIONAL THERAPY": "33",
                "OPS": "2",
                "ORAL AND MAXILLOFACIAL SURGERY": "41",
                "ORTHOPAEDIC MEDICINE": "23",
                "ORTHOPAEDIC SURGERY": "13",
                "OTHER": "14",
                "PHC": "64",
                "PHYSIOTHERAPY": "32",
                "PODIATRY": "36",
                "PSYCHOLOGICAL MEDICINE": "16",
                "RADIOLOGY/X-RAY": "19",
                "REHABILITATION": "17",
                "REHABILITATION MEDICINE": "26",
                "REN": "52",
                "RES": "51",
                "RHI": "58",
                "RHP": "59",
                "SPEECH THERAPY": "34",
                "SPO": "55",
                "SUR": "25",
                "TC": "57",
                "TOX": "49",
                "UROLOGY": "21",
                "VAS": "62"
            };
            
            // Extract specialties from the API data in the terminal output
            const apiSpecialties = [
                "Accident & Emergency", "ANAESTHESIA", "BRE", "CARDIOLOGY", 
                "CMS", "CTR", "CTS", "CV", "DAY HOSPITAL", "DENTISTRY", 
                "DERMATOLOGY", "DIETETIC CONSULTATION", "EAR, NOSE & THROAT", 
                "EYE", "END", "FGD", "GAS", "GASTROENTEROLOGY", "GEM", 
                "GENERAL MEDICINE", "GENERAL SURGERY", "GERIATRIC MEDICINE", 
                "HS", "MED", "MIXED- MED,ORTH, SUR", "MSS", "NEO", "NEUROLOGY", 
                "NEUROSURGERY", "NUEROLOGY", "OBSTERICS AND GYNAECOLOGY", 
                "OCCUPATIONAL THERAPY", "OPS", "ORAL AND MAXILLOFACIAL SURGERY", 
                "ORTHOPAEDIC MEDICINE", "ORTHOPAEDIC SURGERY", "PHYSIOTHERAPY", 
                "PHC", "PODIATRY", "PSYCHOLOGICAL MEDICINE", "RADIOLOGY/X-RAY", 
                "REHABILITATION", "REHABILITATION MEDICINE", "REN", "RES", 
                "RHI", "RHP", "SPEECH THERAPY", "SPO", "SUR", "TC", "TOX", 
                "UROLOGY", "VAS"
            ];
            
            // Track specialties we've already seen to avoid duplicates
            const seenSpecialties = new Set();
            
            // Process the API specialties
            for (const specialty of apiSpecialties) {
                // Skip if already seen (avoid duplicates)
                if (seenSpecialties.has(specialty.toLowerCase())) {
                    continue;
                }
                
                // Add to seen set
                seenSpecialties.add(specialty.toLowerCase());
                
                // Determine category
                let category = "Other Specialties";
                for (const [key, value] of Object.entries(specialtyMapping)) {
                    if (specialty.toUpperCase().includes(key) || key.includes(specialty.toUpperCase())) {
                        category = value;
                        break;
                    }
                }
                
                // Add to appropriate category
                if (categories[category]) {
                    // Store specialty with its ID
                    const specialtyId = specialtyIds[specialty] || "";
                    categories[category].specialties.push({
                        name: specialty,
                        id: specialtyId
                    });
                } else {
                    const specialtyId = specialtyIds[specialty] || "";
                    categories["Other Specialties"].specialties.push({
                        name: specialty,
                        id: specialtyId
                    });
                }
            }
            
            // Create container for specialty categories
            html += `<div class="specialty-categories">`;
            
            // Add categories that have specialties
            for (const [categoryName, categoryData] of Object.entries(categories)) {
                if (categoryData.specialties.length > 0) {
                    html += `
                    <div class="specialty-category">
                        <div class="category-header">
                            <i class="fas ${categoryData.icon}"></i>
                            ${categoryName} Specialties
                        </div>
                        <div class="category-items">`;
                    
                    // Sort specialties alphabetically by name
                    categoryData.specialties.sort((a, b) => a.name.localeCompare(b.name));
                    
                    // Add specialties
                    categoryData.specialties.forEach(specialty => {
                        // Create a structured JSON message for specialty selection
                        const jsonMessage = JSON.stringify({
                            message: "specialty_selected",
                            specialty_id: specialty.id,
                            specialty_name: specialty.name
                        });
                        
                        // Create the specialty item with the correct ID and name
                        html += `
                        <div class="specialty-item" 
                            onclick="sendSpecialtySelection('${specialty.name}', '${specialty.id}')" 
                            title="Click to select ${specialty.name}">
                            <i class="fas fa-check-circle"></i>
                            ${specialty.name}
                        </div>`;
                    });
                    
                    html += `</div></div>`;
                }
            }
            
            html += `</div>`;
            
            // Add prompt at the end
            html += `<p class="appointment-prompt">Would you like me to help you find a specialist for a specific health concern?</p>`;
            
            return html;
        }
        
        // Function to send messages to the API
        async function sendMessage() {
            const userInput = document.getElementById("user-input");
            const message = userInput.value.trim();
            
            if (!message) return;
            
            // Add user message to chat
            addMessage(message, 'user');
            
            // Clear input
            userInput.value = "";
            
            // Show typing indicator
            showTypingIndicator();
            
            try {
                // Try WebSocket first if connected
                if (socket && socket.readyState === WebSocket.OPEN) {
                    socket.send(JSON.stringify({ 
                        message: message,
                        session_id: sessionId 
                    }));
                    addToDebugLog(`Message sent via WebSocket with session ID: ${sessionId}`);
                } else {
                    // Fall back to REST API
                    addToDebugLog("WebSocket not available, using REST API");
                    
                    // Send message to API
                    const response = await fetch('/api/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ 
                            message: message,
                            session_id: sessionId
                        })
                    });
                    
                    const data = await response.json();
                    
                    // Remove typing indicator before adding agent message
                    removeTypingIndicator();
                    
                    // Update session ID if provided in response
                    if (data.session_id) {
                        sessionId = data.session_id;
                        localStorage.setItem('athena_session_id', sessionId);
                        console.log(`Updated session ID from API: ${sessionId}`);
                    }
                    
                    if (data.error) {
                        addMessage("I'm sorry, I encountered an error. Please try again.", 'agent');
                        addToDebugLog(`Error: ${data.error}`);
                    } else {
                        // Add agent response to chat
                        addMessage(data.response, 'agent', true, data.suggested_actions);
                    }
                }
            } catch (error) {
                console.error('Error:', error);
                removeTypingIndicator();
                addMessage('Sorry, I encountered an error processing your request.', 'agent');
                addToDebugLog(`Error: ${error.message}`);
            }
        }
        
        // Function to toggle debug information
        function toggleDebug() {
            const debugLog = document.getElementById("debug-log");
            const isVisible = document.getElementById("show-debug").checked;
            debugLog.style.display = isVisible ? "block" : "none";
        }
        
        // Function to add information to the debug log
        function addToDebugLog(text) {
            const debugLog = document.getElementById("debug-log");
            const logEntry = document.createElement("div");
            logEntry.innerHTML = `${new Date().toLocaleTimeString()}: ${text.replace(/\n/g, '<br>')}`;
            debugLog.appendChild(logEntry);
            
            const divider = document.createElement("hr");
            debugLog.appendChild(divider);
            
            // Scroll to bottom
            debugLog.scrollTop = debugLog.scrollHeight;
        }

        // Close button functionality (just reloads the page for demo)
        document.querySelector('.close-button').addEventListener('click', function() {
            window.location.reload();
        });
        
        // Function to end the chat
        function endChat() {
            // Confirm before clearing chat history
            if (chatHistory.length > 0 && !confirm("Are you sure you want to end this chat? All conversation history will be deleted.")) {
                return;
            }
            
            // Generate a new session ID
            sessionId = generateUUID();
            localStorage.setItem('athena_session_id', sessionId);
            console.log(`Generated new session ID after chat end: ${sessionId}`);
            addToDebugLog(`New session ID created: ${sessionId}`);
            
            // Clear chat history from localStorage
            localStorage.removeItem('athena_chat_history');
            chatHistory = [];
            
            // Clear chat messages from UI
            const chatContainer = document.getElementById("chat-container");
            
            // Keep only the welcome message
            chatContainer.innerHTML = `
            <div class="welcome-message">
                <h2>Hello there!</h2>
                <p>I'm <strong>Athena</strong>, your Smart Health Assistant!</p>
                <p>How can I <span class="highlight">help you today?</span></p>
                
                <div class="quick-actions">
                    <div class="action-button" onclick="sendQuickQuery('I have a health issue. What specialist should I see?')">
                        <i class="fas fa-stethoscope"></i> Ask Specialty
                    </div>
                    <div class="action-button" id="end-chat-btn" onclick="endChat()">
                        <i class="fas fa-trash-alt"></i> End Chat
                    </div>
                </div>
            </div>
            <div class="message-container">
            </div>`;
            
            // Clear debug log
            const debugLog = document.getElementById("debug-log");
            debugLog.innerHTML = "";
            
            // Reconnect WebSocket if needed
            if (!isConnected) {
                connectWebSocket();
            }
            
            // Show success message
            addToDebugLog("Chat history cleared and new session created");
        }

        // Function to override tooltips that might contain IDs
        document.addEventListener('DOMContentLoaded', function() {
            // Override default tooltips for specialty buttons
            document.body.addEventListener('mouseover', function(e) {
                // Check if the target or its parent has a title attribute containing "ID:"
                let target = e.target;
                let foundTooltip = false;
                
                // Check up to 3 levels of parent elements
                for (let i = 0; i < 3; i++) {
                    if (!target) break;
                    
                    if (target.title && target.title.includes('(ID:')) {
                        // Extract just the specialty name without the ID
                        const titleParts = target.title.split('(ID:');
                        if (titleParts.length > 0) {
                            // Set the title to just the specialty name part
                            target.title = titleParts[0].trim();
                            foundTooltip = true;
                        }
                    }
                    
                    target = target.parentElement;
                }
            });
        });

        // Function to complete registration and get patient journey
        function completeRegistration(appointmentId) {
            console.log('🎯 completeRegistration called with appointmentId:', appointmentId);
            
            if (!appointmentId) {
                console.error('No appointment ID provided for registration');
                addMessage("Sorry, I couldn't complete the registration. Missing appointment information.", 'agent');
                return;
            }
            
            // Show loading state
            showTypingIndicator();
            
            // Create request message
            const requestMessage = {
                message: "get_patient_journey",
                appointment_id: appointmentId
            };
            
            // Send request via WebSocket
            if (socket && socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify({
                    message: JSON.stringify(requestMessage),
                    session_id: sessionId
                }));
                console.log('Patient journey request sent via WebSocket with appointment ID:', appointmentId);
            } else {
                console.error('WebSocket not available for patient journey request');
                removeTypingIndicator();
                addMessage("Sorry, I couldn't complete the registration. Please try again.", 'agent');
            }
        }

        // Function to display patient journey information
        function displayPatientJourney(journeyData, visitId) {
            try {
                console.log('🎯 displayPatientJourney called!');
                console.log('🎯 journeyData:', journeyData);
                console.log('🎯 visitId:', visitId);
                
                // Extract journey information
                let journeyInfo = {};
                if (journeyData && journeyData.journey_data && Array.isArray(journeyData.journey_data) && journeyData.journey_data.length > 0) {
                    journeyInfo = journeyData.journey_data[0];
                }
                
                // Extract information with fallbacks
                const queueNumber = journeyInfo.QUEUE_NO || 'N/A';
                const patientName = journeyInfo.NAME || 'N/A';
                const patientId = journeyInfo.IDNO || 'N/A';
                const doctorName = journeyInfo.DOCTOR_NAME || 'N/A';
                const clinicLocation = journeyInfo.CLINIC_LOCATION || 'N/A';
                const clinicDesc = journeyInfo.CLINIC_DESC || 'N/A';
                const discharged = journeyInfo.DISCHARGED || 'N';
                const queueType = journeyInfo.QUEUE_TYPE || 'N/A';
                
                // Parse locations if available
                let locationInfo = '';
                if (journeyInfo.LOCATIONS) {
                    try {
                        const locations = JSON.parse(journeyInfo.LOCATIONS);
                        if (locations && locations.length > 0) {
                            const location = locations[0];
                            locationInfo = `${location.description} - ${location.department}`;
                        }
                    } catch (e) {
                        console.log('Could not parse locations:', e);
                    }
                }
                
                // Create patient journey card HTML
                const journeyHTML = `
                    <div class="patient-journey-card">
                        <div class="journey-header">
                            <i class="fas fa-route"></i>
                            <div class="header-text">
                                <h3>Your Journey</h3>
                                <p>Queue Number: <span class="queue-number">${queueNumber}</span></p>
                            </div>
                        </div>
                        
                        <div class="journey-details">
                            <div class="detail-section">
                                <h4><i class="fas fa-user"></i> Patient Information</h4>
                                <div class="detail-item">
                                    <strong>Name:</strong> ${patientName}
                                </div>
                                <div class="detail-item">
                                    <strong>ID:</strong> ${patientId}
                                </div>
                                <div class="detail-item">
                                    <strong>Visit ID:</strong> ${visitId}
                                </div>
                            </div>
                            
                            <div class="detail-section">
                                <h4><i class="fas fa-user-md"></i> Doctor & Location</h4>
                                <div class="detail-item">
                                    <strong>Doctor:</strong> ${doctorName}
                                </div>
                                <div class="detail-item">
                                    <strong>Location:</strong> ${clinicLocation}
                                </div>
                                <div class="detail-item">
                                    <strong>Clinic:</strong> ${clinicDesc}
                                </div>
                            </div>
                            
                            <div class="detail-section queue-section">
                                <h4><i class="fas fa-list-ol"></i> Queue Information</h4>
                                <div class="queue-display">
                                    <div class="queue-number-large">${queueNumber}</div>
                                    <div class="queue-label">Your Queue Number</div>
                                </div>
                                <div class="detail-item">
                                    <strong>Queue Type:</strong> ${queueType}
                                </div>
                                <div class="detail-item">
                                    <strong>Status:</strong> ${discharged === 'N' ? 'Active' : 'Discharged'}
                                </div>
                            </div>
                            
                            <div class="important-note">
                                <i class="fas fa-info-circle"></i>
                                <span>Please wait for your queue number to be called</span>
                            </div>
                        </div>
                    </div>
                `;
                
                // Add to chat
                addMessage(journeyHTML, 'agent');
                
                // Add a follow-up message
                setTimeout(() => {
                    addMessage("Your registration is complete! Please wait for your queue number to be called.", 'agent');
                }, 1000);
                
            } catch (error) {
                console.error('🚨 Error displaying patient journey:', error);
                addMessage("Registration completed! Please check with the reception for your queue number.", 'agent');
            }
        }

        // Function to display appointment booking confirmation
        function displayAppointmentConfirmation(appointmentDetails, visitId, appointmentId) {
            try {
                console.log('🎯 displayAppointmentConfirmation called!');
                console.log('🎯 appointmentDetails:', appointmentDetails);
                console.log('🎯 visitId:', visitId);
                console.log('🎯 appointmentId:', appointmentId);
                
                // Validate appointmentId
                if (!appointmentId) {
                    console.error('🚨 No appointment ID provided to displayAppointmentConfirmation');
                    appointmentId = 'N/A';
                }
                
                // Handle the correct backend response format: { appointment_data: [...] }
                let appointmentData = {};
                let patientData = {};
                let resourceData = {};
                
                if (appointmentDetails && appointmentDetails.appointment_data && Array.isArray(appointmentDetails.appointment_data)) {
                    const result = appointmentDetails.appointment_data[0];
                    
                    if (result && result.RESULT) {
                        try {
                            const parsedResult = JSON.parse(result.RESULT);
                            console.log('Parsed appointment result:', parsedResult);
                            
                            appointmentData = parsedResult.appointment_details || {};
                            patientData = parsedResult.patient_details || {};
                            resourceData = parsedResult.resource_details || {};
                            
                            console.log('Appointment data:', appointmentData);
                            console.log('Patient data:', patientData);
                            console.log('Resource data:', resourceData);
                        } catch (e) {
                            console.error('Error parsing appointment result:', e);
                            // Continue with empty data objects - will use fallback values
                        }
                    } else {
                        console.log('No RESULT field in appointment data');
                        // Continue with empty data objects - will use fallback values
                    }
                } else {
                    console.log('No appointment data found in expected format');
                    // Continue with empty data objects - will use fallback values
                }
                
                // Extract appointment details dynamically
                const appointmentNo = appointmentData.appointmentno || 'N/A';
                const rawDate = appointmentData.appointmentdate;
                const appointmentTime = appointmentData.requestedtime || 'N/A';
                
                // Format the date and time properly
                let appointmentDate = 'N/A';
                let displayTime = 'N/A';
                let fullDateTime = 'N/A';
                
                console.log('Raw date from backend:', rawDate);
                console.log('Appointment time from backend:', appointmentTime);
                
                if (rawDate) {
                    try {
                        // Parse the date directly from the backend format
                        const dateObj = new Date(rawDate);
                        console.log('🎯 Raw date from backend:', rawDate);
                        console.log('🎯 Parsed date object (UTC):', dateObj.toISOString());
                        
                        // Check if the date is valid
                        if (!isNaN(dateObj.getTime())) {
                            console.log('🎯 Date object time (UTC milliseconds):', dateObj.getTime());
                            
                            // Format the date using Singapore timezone directly from UTC date
                            appointmentDate = dateObj.toLocaleDateString('en-US', {
                                weekday: 'long',
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                                timeZone: 'Asia/Singapore'
                            });
                            
                            // Format the time using Singapore timezone directly from UTC date
                            displayTime = dateObj.toLocaleTimeString('en-US', {
                                hour: '2-digit',
                                minute: '2-digit',
                                hour12: true,
                                timeZone: 'Asia/Singapore'
                            });
                            
                            // Combine date and time for display
                            fullDateTime = `${appointmentDate} at ${displayTime}`;
                            
                            console.log('🎯 Formatted appointment date (Singapore):', appointmentDate);
                            console.log('🎯 Formatted display time (Singapore):', displayTime);
                            console.log('🎯 Full date time (Singapore):', fullDateTime);
                        } else {
                            console.error('Invalid date object created');
                            appointmentDate = rawDate;
                            displayTime = appointmentTime;
                            fullDateTime = `${rawDate} at ${appointmentTime}`;
                        }
                    } catch (e) {
                        console.error('Error formatting date:', e);
                        appointmentDate = rawDate;
                        displayTime = appointmentTime;
                        fullDateTime = `${rawDate} at ${appointmentTime}`;
                    }
                } else {
                    console.log('No raw date provided');
                    fullDateTime = 'N/A';
                }
                
                // Extract dynamic patient info (keeping patient ID same for now as requested)
                const patientName = patientData.name || 'Demo2';
                const patientId = patientData.patientid || 'DEMO002';
                const patientDOB = patientData.dateofbirth || '07/06/1979';
                const patientGender = patientData.gender || 'MALE';
                const patientPhone = patientData.phone || '+************';
                const patientEmail = patientData.email || '<EMAIL>';
                const patientAddress = patientData.address || '2/64 East Street, Ushman Road\nT.Nagar\nChennai, INDIA';
                
                // Extract dynamic doctor and appointment info
                const doctorName = resourceData.Drdescription || (window.selectedAppointment ? window.selectedAppointment.doctor : 'Unknown Doctor');
                const specialty = resourceData.Speciality_DESC || (window.selectedAppointment ? window.selectedAppointment.specialty : 'Unknown Specialty');
                const clinicalLocation = resourceData.clinic_desc || 'CLINIC B-CHI';
                
                console.log('Final appointment details:');
                console.log('- Doctor:', doctorName);
                console.log('- Specialty:', specialty);
                console.log('- Clinical:', clinicalLocation);
                console.log('- Time:', displayTime);
                console.log('- Date:', appointmentDate);
                
                // Create simple single-column appointment confirmation card
                const confirmationHTML = `
                    <div class="appointment-confirmation-card">
                        <div class="confirmation-header">
                            <i class="fas fa-calendar-check"></i>
                            <div class="header-text">
                                <h3>Appointment Booked Successfully!</h3>
                                <p>Appointment No: ${appointmentNo}</p>
                            </div>
                        </div>
                        
                        <div class="appointment-details">
                            <div class="detail-section">
                                <h4><i class="fas fa-calendar-alt"></i> Appointment Information</h4>
                                <div class="detail-item">
                                    <strong>Date & Time:</strong> ${fullDateTime}
                                </div>
                                <div class="detail-item">
                                    <strong>Doctor:</strong> ${doctorName}
                                </div>
                                <div class="detail-item">
                                    <strong>Specialty:</strong> ${specialty}
                                </div>
                                <div class="detail-item">
                                    <strong>Location:</strong> ${clinicalLocation}
                                </div>
                            </div>
                            
                            <div class="detail-section">
                                <h4><i class="fas fa-user"></i> Patient Information</h4>
                                <div class="detail-item">
                                    <strong>Name:</strong> ${patientName}
                                </div>
                                <div class="detail-item">
                                    <strong>ID:</strong> ${patientId}
                                </div>
                                <div class="detail-item">
                                    <strong>DOB/Gender:</strong> ${patientDOB} - ${patientGender}
                                </div>
                            </div>
                            
                            <div class="detail-section">
                                <h4><i class="fas fa-phone"></i> Contact Details</h4>
                                <div class="detail-item">
                                    <strong>Phone:</strong> ${patientPhone}
                                </div>
                                <div class="detail-item">
                                    <strong>Email:</strong> ${patientEmail}
                                </div>
                                <div class="detail-item">
                                    <strong>Address:</strong> ${patientAddress.replace(/\n/g, ', ')}
                                </div>
                            </div>
                            
                            <div class="detail-section doa-toa-section">
                                <h4><i class="fas fa-clipboard-list"></i> Orders Status</h4>
                                <div class="orders-grid">
                                    <div class="order-item">
                                        <strong>DOA:</strong> No DOA orders
                                    </div>
                                    <div class="order-item">
                                        <strong>TOA:</strong> No TOA orders
                                    </div>
                                </div>
                            </div>
                            
                            <div class="important-note">
                                <i class="fas fa-info-circle"></i>
                                <span>Please arrive 10 minutes before your scheduled appointment</span>
                            </div>
                            
                            <div class="action-section">
                                ${appointmentId !== 'N/A' ? 
                                    `<button class="registration-btn" onclick="completeRegistration('${appointmentId}')">
                                        <i class="fas fa-check-circle"></i> Complete Registration
                                    </button>` :
                                    `<button class="registration-btn" disabled style="background-color: #ccc; cursor: not-allowed;">
                                        <i class="fas fa-exclamation-triangle"></i> Registration Unavailable
                                    </button>`
                                }
                            </div>
                        </div>
                    </div>
                `;
                
                console.log('🎯 About to add appointment confirmation to chat');
                console.log('🎯 Confirmation HTML length:', confirmationHTML.length);
                addMessage(confirmationHTML, 'agent');
                console.log('🎯 Appointment confirmation added to chat successfully');
                
                // Clear the selected appointment
                window.selectedAppointment = null;
                
                // Send a follow-up message
                setTimeout(() => {
                    addMessage("Is there anything else I can help you with today?", 'agent');
                }, 2000);
                
            } catch (error) {
                console.error('🚨 Error displaying appointment confirmation:', error);
                console.error('🚨 Error stack:', error.stack);
                addMessage("Your appointment has been successfully booked!", 'agent');
            }
        }

        // Function to get session time for doctor card display
        async function getSessionTimeForDisplay(resourceId, sessionDate, sessionId, doctorCardId) {
            try {
                // Create proper ISO format for the API call
                const sessionDateObj = new Date(sessionDate);
                const sessionDateISO = sessionDateObj.toISOString();
                
                const url = `http://eserver/api/his/AppointmentsAPI/GetSessionSlots?Id=${resourceId}&SessionDate=${encodeURIComponent(sessionDateISO)}&SessionId=${sessionId}`;
                
                const response = await fetch(url, {
                    headers: {
                        'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1laWRlbnRpZmllciI6IjB4MzEzNDM0MzdCMEE2RjcxQS0yRkM0LTRENTYtOTcwOS1EOTlBMjQ1REYyQkIiLCJleHAiOjE3NTIyOTE5NjgsImlzcyI6Iml2aXZhY2FyZS5jb20iLCJhdWQiOiJpdml2YWNhcmUuY29tIn0.REFU7YjAgDEKnT_uNkz7AXGxOC1mdkSla45uzE5b7po'
                    }
                });
                
                if (response.ok) {
                    const slots = await response.json();
                    if (slots && slots.length > 0) {
                        // Get current time
                        const now = new Date();
                        
                        // Find the first available slot that's in the future
                        const futureSlot = slots.find(slot => {
                            if (slot.SLOTTIME && slot.BOOKED === "N") {
                                // Server data is in UTC, Date constructor handles it properly
                                const slotDateTime = new Date(slot.SLOTTIME);
                                return slotDateTime > now;
                            }
                            return false;
                        });
                        
                        if (futureSlot) {
                            // Server data is in UTC, Date constructor handles conversion
                            const slotDateTime = new Date(futureSlot.SLOTTIME);
                            const sessionTime = slotDateTime.toLocaleTimeString('en-US', {
                                hour: '2-digit',
                                minute: '2-digit',
                                hour12: false
                            });
                            
                            console.log(`UTC time: ${futureSlot.SLOTTIME}, Local time: ${sessionTime}`);
                            
                            // Update the doctor card with the session time
                            const doctorCard = document.getElementById(doctorCardId);
                            if (doctorCard) {
                                const sessionInfo = doctorCard.querySelector('.session-info');
                                if (sessionInfo) {
                                    sessionInfo.innerHTML = `
                                        <span class="session-time"><i class="fas fa-clock"></i> ${sessionTime}</span>
                                        ${sessionInfo.innerHTML}
                                    `;
                                }
                            }
                        }
                    }
                }
            } catch (error) {
                console.error('Error fetching session time for display:', error);
            }
        }
    </script>
</body>
</html> 