"""
Chat handler for processing chat requests and responses.
"""

import json
import logging
import re
import uuid
from typing import Dict, Any, Optional, List
from fastapi.responses import JSONResponse

logger = logging.getLogger("ChatHandler")

class ChatHandler:
    """Handles chat processing logic for both HTTP and WebSocket requests."""
    
    def __init__(self, agent):
        """
        Initialize the chat handler.
        
        Args:
            agent: The ReAct agent instance
        """
        self.agent = agent
    
    def process_specialty_selection(self, user_message: str) -> Optional[Dict[str, Any]]:
        """
        Process specialty selection messages.
        
        Args:
            user_message: The user's message
            
        Returns:
            Processed message data or None if not a specialty selection
        """
        try:
            # Try to parse as JSON
            parsed_message = json.loads(user_message)
            if isinstance(parsed_message, dict) and parsed_message.get("message") == "specialty_selected":
                specialty_id = parsed_message.get("specialty_id")
                specialty_name = parsed_message.get("specialty_name", "")
                
                logger.info(f"Detected specialty selection: {specialty_name} (ID: {specialty_id})")
                
                if specialty_id:
                    return {
                        "type": "specialty_selection",
                        "specialty_id": specialty_id,
                        "specialty_name": specialty_name,
                        "processed_message": specialty_name
                    }
                else:
                    logger.warning("Specialty selection missing specialty_id")
        except (json.JSONDecodeError, TypeError):
            # Not a structured message
            pass
        
        return None
    
    def extract_specialty_recommendations(self, response: str) -> Optional[List[Dict[str, Any]]]:
        """
        Extract specialty recommendations from response text.
        
        Args:
            response: The agent's response text
            
        Returns:
            List of specialty options or None if no recommendations found
        """
        if not response or not isinstance(response, str):
            return None
            
        # Check if the response contains specialty recommendations
        if not (("please select a specialty" in response.lower() or 
                "which specialty" in response.lower() or 
                "select the specialty" in response.lower()) and 
               ("• " in response or "**" in response)):
            return None
            
        logger.info("Detected specialty recommendation in response")
        
        # Extract specialty recommendations from the text
        specialty_lines = []
        for line in response.split("\n"):
            line = line.strip()
            if line.startswith("• "):
                specialty_lines.append(line)
                logger.info(f"Found specialty line: {line}")
        
        logger.info(f"Found {len(specialty_lines)} specialty lines")
        
        # Extract specialties and IDs
        specialty_options = []
        # Log all specialties in memory for debugging
        logger.info(f"Specialties in memory: {self.agent.specialty_memory}")
        
        for line in specialty_lines:
            # Try to extract specialty name from patterns like "• NEUROLOGY: " or "• **NEUROLOGY**:"
            # Updated to handle mixed-case specialty names like "Accident & Emergency"
            specialty_match = re.search(r'• (?:\*\*)?([A-Za-z][A-Za-z\s,&\']+)(?:\*\*)?:', line)
            if specialty_match:
                specialty_name = specialty_match.group(1).strip()
                logger.info(f"Extracted specialty name: '{specialty_name}'")
                
                # Look up the specialty ID in the agent's memory
                specialty_id = None
                for name, id_val in self.agent.specialty_memory.items():
                    if name.upper() == specialty_name.upper():
                        specialty_id = id_val
                        logger.info(f"Found matching specialty in memory: {name} with ID {id_val}")
                        break
                    # Also try a case-insensitive match if the exact match fails
                    elif name.upper().strip() == specialty_name.upper().strip():
                        specialty_id = id_val
                        logger.info(f"Found case-insensitive match: {name} with ID {id_val}")
                        break
                
                if specialty_id:
                    specialty_options.append({
                        "label": specialty_name,
                        "value": specialty_id
                    })
                    logger.info(f"Added specialty button option: {specialty_name} (ID: {specialty_id})")
                else:
                    logger.warning(f"Could not find ID for specialty: {specialty_name}")
                    # Try a more flexible match as a fallback
                    for name, id_val in self.agent.specialty_memory.items():
                        if specialty_name.upper() in name.upper() or name.upper() in specialty_name.upper():
                            specialty_id = id_val
                            logger.info(f"Found partial match: {name} with ID {id_val}")
                            specialty_options.append({
                                "label": name,  # Use the name from memory for consistency
                                "value": specialty_id
                            })
                            logger.info(f"Added specialty button option via partial match: {name} (ID: {specialty_id})")
                            break
        
        return specialty_options if specialty_options else None
    
    def create_response_content(self, response: str, session_id: str, 
                              specialty_options: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        Create standardized response content.
        
        Args:
            response: The agent's response
            session_id: The session ID
            specialty_options: Optional specialty selection options
            
        Returns:
            Formatted response content
        """
        response_content = {
            "response": response,
            "session_id": session_id
        }
        
        if specialty_options:
            suggested_actions = [
                {
                    "type": "specialty_selection",
                    "options": specialty_options
                }
            ]
            response_content["suggested_actions"] = suggested_actions
            logger.info(f"Added {len(specialty_options)} specialty button options to response")
        
        return response_content
