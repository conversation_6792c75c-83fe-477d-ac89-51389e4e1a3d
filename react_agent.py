import json
import requests
import os
import logging
import re
import traceback
import uuid
import pendulum
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional
from tools import Tools
from db_connector import DBConnector

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger("ReActAgent")

class ReActAgent:
    """
    ReAct Agent for hospital chatbot using the Reason-Act-Observe paradigm.
    This agent interfaces with a local LLM and uses external tools like doctor specialty lookup.
    It also maintains persistent memory of conversations using MongoDB.
    """
    
    def __init__(self, llm_endpoint: str = "http://192.168.56.1:1234/v1/chat/completions", 
                 specialty_api_endpoint: str = "http://eserver/api/his/AppointmentsAPI/InitAll",
                 specialty_api_token: Optional[str] = None,
                 mongo_uri: Optional[str] = None,
                 debug_mode: bool = True):
        """
        Initialize the ReAct Agent with necessary configurations.
        
        Args:
            llm_endpoint: URL of the LLM API endpoint
            specialty_api_endpoint: URL of the specialty API endpoint
            specialty_api_token: API token for specialty API (optional)
            mongo_uri: MongoDB connection URI (optional)
            debug_mode: Enable debug mode for detailed logging
        """
        self.llm_endpoint = llm_endpoint
        self.specialty_api_endpoint = specialty_api_endpoint
        self.specialty_api_token = specialty_api_token
        self.debug_mode = debug_mode
        
        # Initialize MongoDB connection
        self.db = None
        if mongo_uri:
            self.db = DBConnector(mongo_uri=mongo_uri)
        
        # Initialize tools
        self.tools_manager = Tools(
            specialty_api_endpoint=specialty_api_endpoint,
            specialty_api_token=self.specialty_api_token,
            debug_mode=debug_mode
        )
        
        # Available tools mapping - mapping tool names to methods in Tools class
        self.tools = {
            "get_doctor_specialties": self.tools_manager.get_doctor_specialties,
            "activate_sso": self.tools_manager.activate_sso,
            "search_by_id_number": self.tools_manager.search_by_id_number,
            "get_today_appointments": self.tools_manager.get_today_appointments,
            "get_ongoing_visits": self.tools_manager.get_ongoing_visits,
            "init_appointments": self.tools_manager.init_appointments,
            "get_user_dataset": self.tools_manager.get_user_dataset,
            "get_session_slots": self.tools_manager.get_session_slots,
            "create_walkin": self.tools_manager.create_walkin,
            "get_appointment_number": self.tools_manager.get_appointment_number,
            "create_visit": self.tools_manager.create_visit,
            "get_patient_journey": self.tools_manager.get_patient_journey,
            "get_appointment_followup": self.tools_manager.get_appointment_followup,
            "recommend_specialist": self.tools_manager.recommend_specialist
        }
        
        # Initialize conversation history
        self.conversation_history = []
        
        # Initialize specialty memory and tracking
        self.specialty_memory = {}
        self.last_recommended_specialty = None
        
        # Add pending date request state
        self.pending_date_request = {}  # session_id -> {"specialty_id": id, "specialty_name": name}
        
        # Initialize user specified date storage for health problem queries with dates
        self.user_specified_date = {}  # session_id -> date_string
        
        # Current session ID - will be set when chat() is called
        self.current_session_id = None
        
        # Memory importance thresholds
        self.memory_importance_thresholds = {
            "greeting": 1,          # Low importance
            "specialty_info": 3,    # Medium importance
            "health_problem": 4,    # High importance  
            "appointment": 4,       # High importance
            "patient_info": 5       # Critical importance
        }
        
        # Keywords related to doctor specialties for better detection
        self.specialty_keywords = [
            "doctor", "specialist", "specialty", "specialties", "speciality", 
            "specialities", "department", "medical", "physician", "practitioner",
            "cardio", "heart", "dental", "teeth", "dentist", "neuro", "brain", 
            "ortho", "bone", "pediatric", "children", "emergency", "surgery"
        ]
        
        # Keywords related to appointments for better detection
        self.appointment_keywords = [
            "appointment", "book", "schedule", "slot", "reserve", "visit", 
            "consultation", "meet", "session", "timing", "available", 
            "follow-up", "followup", "checkup", "walkin", "walk-in"
        ]
        
        # Keywords related to health problems/symptoms for better detection
        self.health_problem_keywords = [
            "pain", "ache", "sick", "problem", "issue", "symptom", "condition",
            "suffering", "hurts", "hurt", "suffer", "ailment", "disease",
            "diagnosis", "treatment", "cure", "remedy", "health", "medical",
            "feeling", "ill", "unwell", "have", "experiencing", "worry"
        ]
        
        logger.info("ReAct Agent initialized with debug_mode=%s", debug_mode)

    def initialize_session(self, session_id: Optional[str] = None, user_data: Optional[Dict[str, Any]] = None) -> str:
        """
        Initialize or resume a session.
        
        Args:
            session_id: Existing session ID or None to create new session
            user_data: Optional user metadata
            
        Returns:
            Session ID (either existing or newly created)
        """
        # If no session ID provided, create a new one
        if not session_id:
            session_id = str(uuid.uuid4())
            logger.info(f"Generated new session ID: {session_id}")
        
        # Store session ID
        self.current_session_id = session_id
        
        # Clear conversation history for this session
        self.conversation_history = []
        
        # Try to create session in database
        if self.db.is_connected():
            # Check if session already exists
            existing_memories = self.db.retrieve_memories(session_id, limit=1)
            if existing_memories:
                logger.info(f"Resuming existing session: {session_id}")
                self._load_memories_into_context(session_id)
            else:
                # Create new session in database
                self.db.create_session(session_id, user_data)
                logger.info(f"Created new session in database: {session_id}")
                
                # Store initial greeting memory
                self._store_memory("greeting", "Session initialized", 1)
        
        return session_id
    
    def _load_memories_into_context(self, session_id: str) -> None:
        """
        Load relevant memories from database into agent context.
        
        Args:
            session_id: Session ID to load memories for
        """
        if not self.db.is_connected():
            logger.warning("Database not connected, cannot load memories")
            return
            
        # Get all important memories for this session
        memories = self.db.retrieve_memories(session_id, limit=20)
        
        if not memories:
            logger.info("No memories found for session")
            return
            
        logger.info(f"Loading {len(memories)} memories into context")
        
        # Process specialty memories
        specialty_memories = [m for m in memories if m.get("memory_type") == "specialty_info"]
        for memory in specialty_memories:
            content = memory.get("content", "")
            metadata = memory.get("metadata", {})
            
            # If memory contains specialty mapping
            if "specialty_name" in metadata and "specialty_id" in metadata:
                specialty_name = metadata.get("specialty_name")
                specialty_id = metadata.get("specialty_id")
                if specialty_name and specialty_id:
                    logger.info(f"Loading specialty mapping from memory: {specialty_name} -> {specialty_id}")
                    self._store_specialty_mapping(specialty_name, specialty_id)
        
        # Process health problem memories
        health_memories = [m for m in memories if m.get("memory_type") == "health_problem"]
        if health_memories:
            latest_health_memory = health_memories[0]  # Most recent first
            content = latest_health_memory.get("content", "")
            logger.info(f"Loading health problem from memory: {content}")
            # Could be used later in reasoning
        
        # Process patient info memories
        patient_memories = [m for m in memories if m.get("memory_type") == "patient_info"]
        for memory in patient_memories:
            content = memory.get("content", "")
            logger.info(f"Loading patient info from memory: {content}")
            # Could be used to personalize responses
        
        # Update session activity
        self.db.update_session_activity(session_id)
        
    def _store_memory(self, memory_type: str, content: str, 
                    importance: Optional[int] = None, metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Store a memory in the database.
        
        Args:
            memory_type: Type of memory (e.g., "specialty_info", "health_problem")
            content: Memory content
            importance: Memory importance (1-5)
            metadata: Additional metadata
            
        Returns:
            Memory ID if successful, empty string otherwise
        """
        if not self.db.is_connected() or not self.current_session_id:
            return ""
            
        # Set default importance if not provided
        if importance is None:
            importance = self.memory_importance_thresholds.get(memory_type, 2)
            
        memory_id = self.db.store_memory(
            self.current_session_id, 
            memory_type, 
            content, 
            importance, 
            metadata
        )
        
        logger.info(f"Stored {memory_type} memory (importance: {importance}): {content[:50]}...")
        return memory_id
        
    def _store_specialty_memory(self, specialty_name: str, specialty_id: str) -> None:
        """
        Store specialty information in memory.
        
        Args:
            specialty_name: Name of specialty
            specialty_id: ID of specialty
        """
        metadata = {
            "specialty_name": specialty_name,
            "specialty_id": specialty_id
        }
        
        content = f"Patient is interested in {specialty_name} specialty (ID: {specialty_id})"
        self._store_memory("specialty_info", content, 3, metadata)
        
    def _store_health_problem_memory(self, health_problem: str) -> None:
        """
        Store health problem information in memory.
        
        Args:
            health_problem: Health problem description
        """
        content = f"Patient mentioned health problem: {health_problem}"
        metadata = {
            "health_problem": health_problem
        }
        
        self._store_memory("health_problem", content, 4, metadata)
    
    def _is_date_input(self, user_query: str) -> bool:
        """
        Check if the user's query contains a date input.
        
        Args:
            user_query: The user's input query
            
        Returns:
            True if query contains a date, False otherwise
        """
        try:
            # Check for common date patterns
            date_patterns = [
                r'\d{4}-\d{2}-\d{2}',  # YYYY-MM-DD
                r'\d{2}/\d{2}/\d{4}',  # MM/DD/YYYY or DD/MM/YYYY
                r'\d{2}-\d{2}-\d{4}',  # DD-MM-YYYY or MM-DD-YYYY
                r'\d{1,2}/\d{1,2}/\d{4}',  # M/D/YYYY or D/M/YYYY
                r'\d{1,2}-\d{1,2}-\d{4}',  # D-M-YYYY or M-D-YYYY
            ]
            
            for pattern in date_patterns:
                if re.search(pattern, user_query.strip()):
                    return True
            
            # Check for day of week patterns
            day_patterns = [
                r'\b(monday|tuesday|wednesday|thursday|friday|saturday|sunday)\b',
                r'\b(mon|tue|wed|thu|fri|sat|sun)\b',
                r'\b(today|tomorrow|tommorrow|tomorow|tommorow|next week|this week)\b',  # Include misspellings
                r'\bon\s+(\d{1,2})(st|nd|rd|th)?\b',  # "on 8th", "on 15"
                r'\b(\d{1,2})(st|nd|rd|th)?\s+(january|february|march|april|may|june|july|august|september|october|november|december|jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\b',  # "8th Jan"
                r'\b(january|february|march|april|may|june|july|august|september|october|november|december|jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\s+(\d{1,2})(st|nd|rd|th)?\b',  # "Jan 8th"
            ]
            
            query_lower = user_query.lower()
            for pattern in day_patterns:
                if re.search(pattern, query_lower):
                    return True
                    
            return False
            
        except Exception as e:
            logger.error(f"Error checking date input: {str(e)}")
            return False
    
    def _extract_date_from_query(self, user_query: str) -> str:
        """
        Extract and validate date from user query.
        
        Args:
            user_query: The user's input query
            
        Returns:
            Validated date in YYYY-MM-DD format, or empty string if invalid
        """
        try:
            # Try to parse various date formats using pendulum
            query_clean = user_query.strip()
            query_lower = query_clean.lower()
            
            logger.info(f"Extracting date from query: '{query_clean}'")
            
            # Get current date for reference
            singapore_tz = pendulum.now("Asia/Singapore")
            today = singapore_tz.date()
            
            # Handle special keywords first (including common misspellings)
            if 'today' in query_lower:
                logger.info("Detected 'today' keyword in query")
                return singapore_tz.format("YYYY-MM-DD")
            elif any(word in query_lower for word in ['tomorrow', 'tommorrow', 'tomorow', 'tommorow']):
                logger.info("Detected 'tomorrow' keyword (or variant) in query")
                return singapore_tz.add(days=1).format("YYYY-MM-DD")
            elif 'this week' in query_lower:
                # From today to end of this week (Sunday)
                start_date = singapore_tz
                # Find the next Sunday (end of this week)
                days_until_sunday = (6 - start_date.weekday()) % 7
                if days_until_sunday == 0:  # Today is Sunday
                    days_until_sunday = 0
                end_of_week = start_date.add(days=days_until_sunday)
                date_range = f"{start_date.format('YYYY-MM-DD')}|{end_of_week.format('YYYY-MM-DD')}"
                logger.info(f"Detected 'this week' keyword in query, using date range: {date_range}")
                return date_range
            elif 'next week' in query_lower:
                # From Monday of next week to Sunday of next week
                current_weekday = singapore_tz.weekday()  # Monday = 0, Sunday = 6
                if current_weekday == 0:  # Today is Monday
                    days_until_next_monday = 7
                else:
                    days_until_next_monday = 7 - current_weekday
                start_of_next_week = singapore_tz.add(days=days_until_next_monday)
                end_of_next_week = start_of_next_week.add(days=6)  # Sunday of next week
                date_range = f"{start_of_next_week.format('YYYY-MM-DD')}|{end_of_next_week.format('YYYY-MM-DD')}"
                logger.info(f"Detected 'next week' keyword in query, using date range: {date_range}")
                return date_range
            
            # Handle "8th Jan", "Jan 8th", "31 aug", "aug 31", "12th september" patterns FIRST
            # This should be checked before the ordinal-only patterns
            month_day_patterns = [
                # Pattern with optional year: "12th September 2026", "31 aug 2025", etc.
                r'\b(\d{1,2})(st|nd|rd|th)?\s+(january|february|march|april|may|june|july|august|september|october|november|december|jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)(?:\s+(\d{4}))?\b',
                # Pattern with optional year: "September 12th 2026", "aug 31 2025", etc.
                r'\b(january|february|march|april|may|june|july|august|september|october|november|december|jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\s+(\d{1,2})(st|nd|rd|th)?(?:\s+(\d{4}))?\b'
            ]
            
            month_mapping = {
                'jan': 1, 'january': 1, 'feb': 2, 'february': 2, 'mar': 3, 'march': 3, 
                'apr': 4, 'april': 4, 'may': 5, 'jun': 6, 'june': 6,
                'jul': 7, 'july': 7, 'aug': 8, 'august': 8, 'sep': 9, 'september': 9, 
                'oct': 10, 'october': 10, 'nov': 11, 'november': 11, 'dec': 12, 'december': 12
            }
            
            for pattern in month_day_patterns:
                match = re.search(pattern, query_lower)
                if match:
                    groups = match.groups()
                    logger.info(f"Month-day pattern matched: {groups}")
                    
                    # Extract day, month, and optional year
                    if groups[0].isdigit():  # "31 aug" or "12th september" format
                        day_num = int(groups[0])
                        month_name = groups[2]  # Skip groups[1] which is the ordinal suffix
                        year_str = groups[3] if len(groups) > 3 and groups[3] else None
                        logger.info(f"Day-month format detected: day={day_num}, month_name='{month_name}', year={year_str}")
                    else:  # "aug 31" or "september 12th" format
                        month_name = groups[0]
                        day_num = int(groups[1])
                        year_str = groups[3] if len(groups) > 3 and groups[3] else None
                        logger.info(f"Month-day format detected: month_name='{month_name}', day={day_num}, year={year_str}")
                    
                    # Validate day number
                    if not (1 <= day_num <= 31):
                        logger.warning(f"Invalid day number: {day_num}")
                        return "DATE_IN_PAST"  # Return error for invalid day
                    
                    # Validate month name
                    if month_name not in month_mapping:
                        logger.warning(f"Invalid or misspelled month: {month_name}")
                        return "DATE_IN_PAST"  # Return error for invalid month
                    
                    month_num = month_mapping[month_name]
                    logger.info(f"Mapped month '{month_name}' to number {month_num}")
                    
                    # Determine the year to use
                    if year_str:
                        target_year = int(year_str)
                        logger.info(f"Using specified year: {target_year}")
                    else:
                        # Use current year first, then next year if date is in past
                        target_year = singapore_tz.year
                        logger.info(f"Using current year: {target_year}")
                    
                    try:
                        # Try to create the date with proper validation
                        target_date = singapore_tz.replace(year=target_year, month=month_num, day=day_num)
                        logger.info(f"Created target date: {target_date.format('YYYY-MM-DD')}")
                        
                        # Check if the date is valid (this will catch Feb 29 on non-leap years, etc.)
                        if target_date.month != month_num or target_date.day != day_num:
                            logger.warning(f"Invalid date: {day_num}/{month_num}/{target_year}")
                            return "DATE_IN_PAST"  # Return error for invalid date
                        
                        # If year was specified, validate it's not too far in future
                        if year_str:
                            if target_date.year > singapore_tz.year + 1:
                                logger.warning(f"Date {target_date.format('YYYY-MM-DD')} is more than 1 year from today")
                                return "DATE_TOO_FAR"
                            elif target_date.date() < today:
                                logger.warning(f"Date {target_date.format('YYYY-MM-DD')} is in the past")
                                return "DATE_IN_PAST"
                        else:
                            # No year specified, check current year first
                            if target_date.date() >= today:
                                # Check if date is within 1 year from today
                                one_year_from_today = today.add(years=1)
                                if target_date.date() <= one_year_from_today:
                                    logger.info(f"Date {target_date.format('YYYY-MM-DD')} is valid for current year")
                                    return target_date.format("YYYY-MM-DD")
                                else:
                                    logger.warning(f"Date {target_date.format('YYYY-MM-DD')} is more than 1 year from today")
                                    return "DATE_TOO_FAR"
                            else:
                                # Try next year
                                try:
                                    target_date = singapore_tz.add(years=1).replace(month=month_num, day=day_num)
                                    logger.info(f"Date is in past, trying next year: {target_date.format('YYYY-MM-DD')}")
                                    
                                    # Validate the next year date is also valid
                                    if target_date.month != month_num or target_date.day != day_num:
                                        logger.warning(f"Invalid date for next year: {day_num}/{month_num}/{target_date.year}")
                                        return "DATE_IN_PAST"
                                    
                                    # Check if next year date is within 1 year from today
                                    one_year_from_today = today.add(years=1)
                                    if target_date.date() <= one_year_from_today:
                                        return target_date.format("YYYY-MM-DD")
                                    else:
                                        logger.warning(f"Date {target_date.format('YYYY-MM-DD')} is more than 1 year from today")
                                        return "DATE_TOO_FAR"
                                except Exception as e:
                                    logger.error(f"Error creating date for next year with month={month_num}, day={day_num}: {str(e)}")
                                    return "DATE_IN_PAST"
                        
                        # If we reach here with a specified year, return the date
                        if year_str:
                            return target_date.format("YYYY-MM-DD")
                            
                    except Exception as e:
                        logger.error(f"Error creating date with year={target_year}, month={month_num}, day={day_num}: {str(e)}")
                        # This catches invalid dates like Feb 29 on non-leap years
                        return "DATE_IN_PAST"
            
            # Handle day of week patterns
            day_patterns = [
                r'\b(monday|tuesday|wednesday|thursday|friday|saturday|sunday)\b',
                r'\b(mon|tue|wed|thu|fri|sat|sun)\b'
            ]
            
            for pattern in day_patterns:
                match = re.search(pattern, query_lower)
                if match:
                    day_name = match.group(1)
                    logger.info(f"Day of week pattern matched: {day_name}")
                    # Map short forms to full names
                    day_mapping = {
                        'mon': 'monday', 'tue': 'tuesday', 'wed': 'wednesday',
                        'thu': 'thursday', 'fri': 'friday', 'sat': 'saturday', 'sun': 'sunday'
                    }
                    if day_name in day_mapping:
                        day_name = day_mapping[day_name]
                    
                    # Find the next occurrence of this day
                    target_date = self._find_next_weekday(singapore_tz, day_name)
                    if target_date:
                        logger.info(f"Found next {day_name}: {target_date.format('YYYY-MM-DD')}")
                        return target_date.format("YYYY-MM-DD")
            
            # Handle "on 8th" or "on 15" patterns (only when no month is specified)
            # Make this more specific to avoid matching "on 12th september"
            ordinal_match = re.search(r'\bon\s+(\d{1,2})(st|nd|rd|th)?\b(?!\s+(?:january|february|march|april|may|june|july|august|september|october|november|december|jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec))', query_lower)
            if ordinal_match:
                day_num = int(ordinal_match.group(1))
                logger.info(f"Ordinal pattern matched (without month): day={day_num}")
                if 1 <= day_num <= 31:
                    # Try current month first, then next month if day has passed
                    current_month_date = singapore_tz.replace(day=1).add(days=day_num-1)
                    if current_month_date.date() >= today:
                        logger.info(f"Using current month for day {day_num}: {current_month_date.format('YYYY-MM-DD')}")
                        return current_month_date.format("YYYY-MM-DD")
                    else:
                        # Try next month
                        next_month_date = singapore_tz.add(months=1).replace(day=1).add(days=day_num-1)
                        logger.info(f"Using next month for day {day_num}: {next_month_date.format('YYYY-MM-DD')}")
                        return next_month_date.format("YYYY-MM-DD")
            
            # Common date patterns and their expected formats
            date_patterns = [
                (r'(\d{4}-\d{2}-\d{2})', 'YYYY-MM-DD'),  # YYYY-MM-DD
                (r'(\d{2}/\d{2}/\d{4})', 'DD/MM/YYYY'),  # DD/MM/YYYY
                (r'(\d{2}-\d{2}-\d{4})', 'DD-MM-YYYY'),  # DD-MM-YYYY
                (r'(\d{1,2}/\d{1,2}/\d{4})', 'D/M/YYYY'),  # D/M/YYYY
                (r'(\d{1,2}-\d{1,2}-\d{4})', 'D-M-YYYY'),  # D-M-YYYY
            ]
            
            for pattern, fmt in date_patterns:
                match = re.search(pattern, query_clean)
                if match:
                    date_str = match.group(1)
                    logger.info(f"Numerical date pattern matched: {date_str} with format {fmt}")
                    try:
                        # Try to parse the date using pendulum's flexible parsing
                        if fmt == 'YYYY-MM-DD':
                            parsed_date = pendulum.parse(date_str, strict=False)
                        elif fmt in ['DD/MM/YYYY', 'D/M/YYYY']:
                            # Split by / and reorder to YYYY-MM-DD
                            parts = date_str.split('/')
                            if len(parts) == 3:
                                day, month, year = parts
                                iso_date = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                                parsed_date = pendulum.parse(iso_date, strict=False)
                            else:
                                continue
                        elif fmt in ['DD-MM-YYYY', 'D-M-YYYY']:
                            # Split by - and reorder to YYYY-MM-DD
                            parts = date_str.split('-')
                            if len(parts) == 3:
                                day, month, year = parts
                                iso_date = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                                parsed_date = pendulum.parse(iso_date, strict=False)
                            else:
                                continue
                        else:
                            continue
                        
                        # Check if the date is valid and not in the past
                        if parsed_date.date() < today:
                            logger.warning(f"Date {date_str} is in the past")
                            return "DATE_IN_PAST"
                        
                        # Check if date is within 1 year from today
                        one_year_from_today = today.add(years=1)
                        if parsed_date.date() > one_year_from_today:
                            logger.warning(f"Date {date_str} is more than 1 year from today")
                            return "DATE_TOO_FAR"
                        
                        # Return in YYYY-MM-DD format
                        logger.info(f"Successfully parsed date: {parsed_date.format('YYYY-MM-DD')}")
                        return parsed_date.format("YYYY-MM-DD")
                        
                    except Exception as e:
                        logger.warning(f"Failed to parse date {date_str} with format {fmt}: {str(e)}")
                        continue
                        
            logger.info("No date pattern matched")
            return ""
            
        except Exception as e:
            logger.error(f"Error extracting date from query: {str(e)}")
            return ""
    
    def _set_pending_date_request(self, session_id: str, specialty_id: str, specialty_name: str) -> None:
        """
        Set a pending date request for a session.
        
        Args:
            session_id: The session ID
            specialty_id: The specialty ID
            specialty_name: The specialty name
        """
        self.pending_date_request[session_id] = {
            "specialty_id": specialty_id,
            "specialty_name": specialty_name
        }
        logger.info(f"Set pending date request for session {session_id}: {specialty_name} (ID: {specialty_id})")
    
    def _get_pending_date_request(self, session_id: str) -> Dict[str, str]:
        """
        Get pending date request for a session.
        
        Args:
            session_id: The session ID
            
        Returns:
            Dictionary with specialty_id and specialty_name, or empty dict if none
        """
        return self.pending_date_request.get(session_id, {})
    
    def _clear_pending_date_request(self, session_id: str) -> None:
        """
        Clear pending date request for a session.
        
        Args:
            session_id: The session ID
        """
        if session_id in self.pending_date_request:
            del self.pending_date_request[session_id]
            logger.info(f"Cleared pending date request for session {session_id}")
    
    def _find_next_weekday(self, current_date: pendulum.DateTime, target_day: str) -> pendulum.DateTime:
        """
        Find the next occurrence of a specific weekday.
        
        Args:
            current_date: The current date
            target_day: The target day name (e.g., 'monday', 'tuesday')
            
        Returns:
            The next occurrence of the target day
        """
        try:
            # Map day names to weekday numbers (Monday = 0, Sunday = 6)
            day_mapping = {
                'monday': 0, 'tuesday': 1, 'wednesday': 2, 'thursday': 3,
                'friday': 4, 'saturday': 5, 'sunday': 6
            }
            
            if target_day not in day_mapping:
                return None
            
            target_weekday = day_mapping[target_day]
            current_weekday = current_date.weekday()
            
            # Calculate days until target day
            days_ahead = target_weekday - current_weekday
            
            # If target day is today or has passed this week, go to next week
            if days_ahead <= 0:
                days_ahead += 7
            
            return current_date.add(days=days_ahead)
            
        except Exception as e:
            logger.error(f"Error finding next weekday: {str(e)}")
            return None
    
    def _reason(self, user_query: str) -> Dict[str, Any]:
        """
        Reason about the next step based on the user query and conversation history.
        This method prompts the LLM to decide what to do next.
        
        Args:
            user_query: The user's input query
            
        Returns:
            Dict containing the reasoning and next action
        """
        logger.info("\n=== REASONING ===")
        
        # First check if user is providing a date when we're waiting for one
        if self.current_session_id and self._get_pending_date_request(self.current_session_id):
            pending_request = self._get_pending_date_request(self.current_session_id)
            
            if self._is_date_input(user_query):
                logger.info("User provided date input while date request is pending")
                
                date_str = self._extract_date_from_query(user_query)
                if date_str:
                    logger.info(f"Valid date extracted: {date_str}")
                    
                    # Clear pending request
                    self._clear_pending_date_request(self.current_session_id)
                    
                    # Fetch doctors for the specified date
                    specialty_id = pending_request.get("specialty_id")
                    specialty_name = pending_request.get("specialty_name")
                    
                    return {
                        "reasoning": f"The user provided a valid date ({date_str}) for {specialty_name} specialty. I should retrieve available doctors for this date.",
                        "use_tool": True,
                        "action": {
                            "action_type": "get_user_dataset",
                            "parameters": {
                                "resource_type": "1",
                                "specialty_id": specialty_id,
                                "date_from": date_str,
                                "date_to": date_str,
                                "query_name": "APPTFINDRESO_AI"
                            }
                        }
                    }
                else:
                    logger.warning("Invalid date format provided")
                    specialty_name = pending_request.get("specialty_name", "Selected specialty")
                    return {
                        "reasoning": "The user provided an invalid date format. I should ask for a correct date format.",
                        "use_tool": False,
                        "direct_answer": f"I couldn't understand the date format. Please provide a date in one of these formats:\n• YYYY-MM-DD (e.g., 2025-07-15)\n• DD/MM/YYYY (e.g., 15/07/2025)\n• DD-MM-YYYY (e.g., 15-07-2025)\n\nPlease enter the date you'd like to check {specialty_name} doctor availability for:"
                    }
            else:
                # User didn't provide a date, remind them
                specialty_name = pending_request.get("specialty_name", "Selected specialty")
                return {
                    "reasoning": "The user didn't provide a date when we're waiting for one. I should remind them.",
                    "use_tool": False,
                    "direct_answer": f"Please provide a date to check {specialty_name} doctor availability. Use one of these formats:\n• YYYY-MM-DD (e.g., 2025-07-15)\n• DD/MM/YYYY (e.g., 15/07/2025)\n• DD-MM-YYYY (e.g., 15-07-2025)"
                }
        
        # First check if this is a general greeting or simple question
        if self._is_greeting(user_query):
            logger.info("Detected greeting or simple question, providing direct answer")
            
            return {
                "reasoning": "The user is providing a greeting or asking a simple question. I can answer directly without using a tool.",
                "use_tool": False,
                "direct_answer": self._get_greeting_response(user_query)
            }
        
        # Check for specialty selection after recommendation
        if self._is_specialty_selection_query(user_query):
            logger.info("Detected specialty selection query, extracting selected specialty")
            
            # Check if this is a structured message with specialty_id directly included
            try:
                import json
                message_data = json.loads(user_query)
                if isinstance(message_data, dict) and message_data.get("message") == "specialty_selected":
                    specialty_id = message_data.get("specialty_id")
                    if specialty_id:
                        logger.info(f"User selected specialty via button with ID: {specialty_id}")
                        
                        # Try to find specialty name for the provided ID
                        specialty_name = "Selected specialty"
                        for name, id_val in self.specialty_memory.items():
                            if id_val == specialty_id:
                                specialty_name = name
                                break
                        
                        # Check if we have a stored date from the initial health problem query
                        stored_date = None
                        if hasattr(self, 'user_specified_date') and self.current_session_id in self.user_specified_date:
                            stored_date = self.user_specified_date[self.current_session_id]
                            logger.info(f"Found stored date from health problem query: {stored_date}")
                        
                        if stored_date:
                            # Check if stored_date is a date range (contains "|")
                            if "|" in stored_date:
                                date_from, date_to = stored_date.split("|")
                                date_range_text = f"from {date_from} to {date_to}"
                            else:
                                date_from = stored_date
                                date_to = stored_date
                                date_range_text = stored_date
                            
                            # Use the stored date and fetch doctors directly
                            return {
                                "reasoning": f"The user has selected the {specialty_name} specialty and we have a stored date range ({date_range_text}). I should retrieve available doctors for this period.",
                                "use_tool": True,
                                "action": {
                                    "action_type": "get_user_dataset",
                                    "parameters": {
                                        "resource_type": "1",
                                        "specialty_id": specialty_id,
                                        "date_from": date_from,
                                        "date_to": date_to,
                                        "query_name": "APPTFINDRESO_AI"
                                    }
                                }
                            }
                        else:
                            # Use today's date by default instead of asking for a date
                            current_date = pendulum.now("Asia/Singapore").format("YYYY-MM-DD")
                            logger.info(f"No stored date found, using current date: {current_date}")
                            
                            return {
                                "reasoning": f"The user has selected the {specialty_name} specialty. I should retrieve available doctors for today ({current_date}).",
                                "use_tool": True,
                                "action": {
                                    "action_type": "get_user_dataset",
                                    "parameters": {
                                        "resource_type": "1",
                                        "specialty_id": specialty_id,
                                        "date_from": current_date,
                                        "date_to": current_date,
                                        "query_name": "APPTFINDRESO_AI"
                                    }
                                }
                            }
                        
            except (json.JSONDecodeError, TypeError):
                # Not a structured message, proceed with text-based extraction
                pass
            
            selected_specialty = self._extract_selected_specialty(user_query)
            
            # Log all available specialties and their IDs
            logger.info(f"Available specialties in memory: {self.specialty_memory}")
            
            if selected_specialty and selected_specialty in self.specialty_memory:
                specialty_id = self.specialty_memory[selected_specialty]
                logger.info(f"User selected specialty: {selected_specialty} (ID: {specialty_id})")
                
                # Check if we have a valid specialty ID
                if not specialty_id:
                    logger.info(f"No valid ID found for specialty '{selected_specialty}'. Asking user to try another specialty.")
                    return {
                        "reasoning": f"The user selected '{selected_specialty}', but we don't have an ID for this specialty. I should inform them that this specialty might not be available.",
                        "use_tool": False,
                        "direct_answer": f"I'm sorry, but it seems that {selected_specialty} specialists aren't available at our hospital at the moment. Would you like to try a different specialty from the list I provided earlier?"
                    }
                
                # Check if we have a stored date from the initial health problem query
                stored_date = None
                if hasattr(self, 'user_specified_date') and self.current_session_id in self.user_specified_date:
                    stored_date = self.user_specified_date[self.current_session_id]
                    logger.info(f"Found stored date from health problem query: {stored_date}")
                
                if stored_date:
                    # Check if stored_date is a date range (contains "|")
                    if "|" in stored_date:
                        date_from, date_to = stored_date.split("|")
                        date_range_text = f"from {date_from} to {date_to}"
                    else:
                        date_from = stored_date
                        date_to = stored_date
                        date_range_text = stored_date
                    
                    # Use the stored date and fetch doctors directly
                    return {
                        "reasoning": f"The user has selected the {selected_specialty} specialty and we have a stored date range ({date_range_text}). I should retrieve available doctors for this period.",
                        "use_tool": True,
                        "action": {
                            "action_type": "get_user_dataset",
                            "parameters": {
                                "resource_type": "1",
                                "specialty_id": specialty_id,
                                "date_from": date_from,
                                "date_to": date_to,
                                "query_name": "APPTFINDRESO_AI"
                            }
                        }
                    }
                else:
                    # Use today's date by default instead of asking for a date
                    current_date = pendulum.now("Asia/Singapore").format("YYYY-MM-DD")
                    logger.info(f"No stored date found, using current date: {current_date}")
                    
                    return {
                        "reasoning": f"The user has selected the {selected_specialty} specialty. I should retrieve available doctors for today ({current_date}).",
                        "use_tool": True,
                        "action": {
                            "action_type": "get_user_dataset",
                            "parameters": {
                                "resource_type": "1",
                                "specialty_id": specialty_id,
                                "date_from": current_date,
                                "date_to": current_date,
                                "query_name": "APPTFINDRESO_AI"
                            }
                        }
                    }
        
        # Check for requests to list all specialties
        if self._is_list_all_specialties_query(user_query):
            logger.info("Detected request to list all specialties")
            
            return {
                "reasoning": "The user is requesting a full list of all available doctor specialties. I should use the get_doctor_specialties tool to retrieve complete specialty information.",
                "use_tool": True,
                "action": {
                    "action_type": "get_doctor_specialties",
                    "parameters": {
                        "query": user_query,
                        "is_full_list": True,
                        "list_all": True
                    }
                }
            }
        
        # Check for health problem queries to recommend specialists (prioritize this over doctor availability)
        if self._is_health_problem_query(user_query):
            logger.info("Detected health problem question, using recommend_specialist tool")
            
            # Extract the health problem from query
            health_problem = self._extract_health_problem(user_query)
            
            # Ensure we have a health problem (this is a safety measure)
            if not health_problem or not health_problem.strip():
                logger.warning("Extracted health problem is empty")
                return {
                    "reasoning": "The user's query seems to be about a health issue, but I couldn't extract a specific health problem.",
                    "use_tool": False,
                    "direct_answer": "To recommend the most appropriate specialist, could you please tell me more about your specific symptoms or health concerns? For example, what symptoms are you experiencing?"
                }
            
            # Check if the health problem is too vague
            if self._is_vague_health_problem(health_problem):
                logger.info("Health issue is too vague, asking for more details")
                return {
                    "reasoning": "The user is asking about what specialist to see for a general health issue. I should ask for more specific symptoms to provide a meaningful recommendation.",
                    "use_tool": False,
                    "direct_answer": "To recommend the most appropriate specialist, could you please tell me more about your specific symptoms or health concerns? For example, where do you feel pain or discomfort, or what symptoms are you experiencing?"
                }
            
            logger.info(f"Extracted health problem: '{health_problem}'")
            
            # Check if the query also contains date information
            extracted_date = ""
            if self._is_date_input(user_query):
                extracted_date = self._extract_date_from_query(user_query)
                logger.info(f"Extracted date from health problem query: '{extracted_date}'")
                
                # Handle special date validation results
                if extracted_date == "DATE_IN_PAST":
                    return {
                        "reasoning": "The user specified a date that is in the past, which is not valid for booking appointments.",
                        "use_tool": False,
                        "direct_answer": "I'm sorry, but the date you specified is in the past. Please provide a future date for your appointment. You can use formats like:\n• YYYY-MM-DD (e.g., 2025-08-31)\n• DD/MM/YYYY (e.g., 31/08/2025)\n• Or simply say 'tomorrow', 'next Monday', 'next week', etc."
                    }
                elif extracted_date == "DATE_TOO_FAR":
                    return {
                        "reasoning": "The user specified a date that is more than 1 year from today, which is beyond our booking window.",
                        "use_tool": False,
                        "direct_answer": "I'm sorry, but the date you specified is more than 1 year from today. We can only book appointments up to 1 year in advance. Please provide a date within the next 12 months."
                    }
            
            # If no date extracted, use current date as default
            if not extracted_date:
                extracted_date = pendulum.now("Asia/Singapore").format("YYYY-MM-DD")
                logger.info(f"No date specified, using current date: '{extracted_date}'")
            
            # Store the extracted date for later use
            if extracted_date and self.current_session_id:
                if not hasattr(self, 'user_specified_date'):
                    self.user_specified_date = {}
                self.user_specified_date[self.current_session_id] = extracted_date
                date_info = f"date range {extracted_date}" if "|" in extracted_date else f"date {extracted_date}"
                logger.info(f"Stored user specified {date_info} for session {self.current_session_id}")
            
            return {
                "reasoning": "The user is asking about what specialist to see for a health issue. I should use the recommend_specialist tool to provide an appropriate recommendation.",
                "use_tool": True,
                "action": {
                    "action_type": "recommend_specialist",
                    "parameters": {"health_problem": health_problem}
                }
            }
        
        # Then check for appointment-related queries
        if self._is_appointment_query(user_query):
            logger.info("Detected appointment-related question, selecting appropriate appointment tool")
            
            # Select the appropriate tool based on the query 
            tool_action = self._select_appointment_tool(user_query)
            
            return {
                "reasoning": f"The user is asking about appointments. I should use the {tool_action['action_type']} tool.",
                "use_tool": True,
                "action": tool_action
            }
        
        # Check for doctor availability request after specialist recommendation
        if self._is_doctor_availability_request(user_query):
            logger.info("Detected doctor availability request after specialist recommendation")
            
            # Extract specialty from conversation history if available
            specialty_id = self._extract_specialty_from_history()
            specialty_name = self._get_specialty_name_from_id(specialty_id)
            
            logger.info(f"Found specialty ID {specialty_id} for '{specialty_name}', proceeding with doctor availability lookup")
            
            # Get current date in YYYY-MM-DD format (Singapore timezone UTC+8)
            singapore_tz = timezone(timedelta(hours=8))
            current_date = datetime.now(singapore_tz).strftime("%Y-%m-%d")
            
            return {
                "reasoning": f"The user wants to see available {specialty_name} doctors. I should use the get_user_dataset tool to retrieve doctor availability.",
                "use_tool": True,
                "action": {
                    "action_type": "get_user_dataset",
                    "parameters": {
                        "resource_type": "1",
                        "specialty_id": specialty_id,
                        "date_from": current_date,
                        "date_to": current_date,
                        "query_name": "APPTFINDRESO_AI"
                    }
                }
            }
        
        # Then check for specialty-related queries
        if self._is_specialty_query(user_query):
            logger.info("Detected specialty-related question, enforcing API call for ReAct flow")
            
            # Extract potential specialty from query
            specialty_query = user_query
            
            # Check if this is specifically a request for a full list or more specialties
            is_full_list = False
            user_query_lower = user_query.lower()
            
            if any(term in user_query_lower for term in ["full list", "all specialties", "show all"]):
                is_full_list = True
                logger.info("User requested full specialty list")
            elif any(term in user_query_lower for term in ["show more", "more specialties", "continue", "next"]):
                is_full_list = True
                logger.info("User requested more specialties (continuation)")
                
            return {
                "reasoning": "The user is asking about doctor specialties. I should use the get_doctor_specialties tool to retrieve accurate information from our API.",
                "use_tool": True,
                "action": {
                    "action_type": "get_doctor_specialties",
                    "parameters": {
                        "query": specialty_query,
                        "is_full_list": is_full_list
                    }
                }
            }
        
        # For queries that don't match any patterns, use the LLM
        logger.info("Query doesn't match specific patterns. Checking with LLM...")
        
        try:
            # Construct a prompt specifically designed to prevent hallucination
            prompt = self._construct_reasoning_prompt(user_query)
            
            logger.info("Calling LLM for reasoning...")
            response = self._call_llm(prompt)
            
            # Parse the LLM response to extract reasoning and action
            reasoning_output = self._parse_reasoning_response(response)
            
            logger.info(f"Reasoning: {reasoning_output.get('reasoning', 'No reasoning provided')}")
            return reasoning_output
        except Exception as e:
            logger.error(f"Error in reasoning step: {str(e)}")
            # Provide a fallback reasoning response when LLM call fails
            return {
                "reasoning": "Failed to reason about the query due to an error.",
                "use_tool": False,
                "direct_answer": "I'm having trouble processing your request right now. Could you try rephrasing your question, or ask about our hospital's specialties or appointment options?"
            }
    
    def _is_greeting(self, query: str) -> bool:
        """
        Determine if a query is a simple greeting or general question.
        
        Args:
            query: The user query to check
            
        Returns:
            Boolean indicating if the query is a greeting
        """
        query_lower = query.lower()
        
        greeting_patterns = [
            r"^(hi|hello|hey|greetings|good morning|good afternoon|good evening)[\s\W]*$",
            r"^how are you(\?)?$",
            r"^what'?s up(\?)?$",
            r"(nice to meet you|pleased to meet you)(\.)$"
        ]
        
        for pattern in greeting_patterns:
            if re.search(pattern, query_lower):
                return True
                
        return False
    
    def _get_greeting_response(self, query: str) -> str:
        """
        Generate a response for greetings.
        
        Args:
            query: The user's greeting
            
        Returns:
            A greeting response
        """
        query_lower = query.lower()
        
        if re.search(r"how are you", query_lower):
            return "I'm doing well, thank you for asking! As SmartClinic Hospital's virtual assistant, I'm here to help with questions about our doctor specialties and appointments. How can we assist you today?"
            
        return "Hello! I'm Athena, SmartClinic Hospital's virtual assistant. I'm here to help you with information about our doctor specialties, finding the right specialist for your health concerns, and managing appointments. How can we assist you today?"
    
    def _is_health_problem_query(self, query: str) -> bool:
        """
        Determine if a query is related to a health problem or symptom.
        
        Args:
            query: The user query to check
            
        Returns:
            Boolean indicating if the query is about a health problem
        """
        query_lower = query.lower()
        
        # Check for specific symptoms directly mentioned (common symptoms)
        common_symptoms = [
            "headache", "migraine", "pain", "ache", "fever", "cough", "dizziness", "cold", "flu",
            "nausea", "vomiting", "rash", "swelling", "infection", "injury", "wound",
            "bleeding", "breathless", "tired", "fatigue", "weakness", "numbness",
            "tingling", "burn", "bruise", "sprain", "fracture", "broken", "cramping", 
            "mental", "anxiety", "depression", "stress", "joint pain", "back pain","eye pain","kidney stones",
            "chest pain", "seizures", "trauma", "bleeding", "burns", "shortness of breath", "stroke", "fainting", "overdose", "acute allergic reaction",
            "cough", "fever", "fatigue", "sore throat", "headache", "mild infections", "nausea", "diarrhea", "runny nose", "general check-up",
            "hernia", "gallstones", "appendicitis", "GI tumors", "bowel obstruction", "severe abdominal pain", "peritonitis", "rectal bleeding", "anal fissures",
            "breast lump", "nipple discharge", "breast pain", "swelling", "redness", "infection", "skin dimpling", "abnormal mammogram", "breast asymmetry",
            "tumor evaluation", "biopsy referral", "suspicious mass", "lymph node swelling", "cancer screening", "unexplained weight loss",
            "valve disorders", "coronary artery disease", "lung nodules", "pleural effusion", "collapsed lung", "thoracic tumors",
            "toothache", "cavities", "gum bleeding", "jaw pain", "mouth ulcers", "sensitivity", "bad breath", "loose teeth",
            "sinusitis", "ear pain", "nasal congestion", "nosebleeds", "tinnitus", "hoarseness", "tonsillitis", "vertigo", "ear discharge",
            "blurry vision", "red eyes", "eye pain", "dryness", "floaters", "sensitivity to light", "glaucoma", "cataracts", "itching", "discharge",
            "memory loss", "confusion", "falls", "incontinence", "joint pain", "frailty", "sleep disturbance", "polypharmacy", "depression", "loss of appetite",
            "diabetes", "bp", "anemia", "body aches", "injury", "back pain", "general surgery consult",
            "fractures", "arthritis", "joint dislocation", "torn ligaments", "carpal tunnel", "hip pain", "scoliosis", "sports injuries",
            "rare diseases", "multi-system symptoms", "undiagnosed issues", "complex cases", "orphan conditions",
            "heel pain", "flat feet", "bunions", "calluses", "corns", "fungal toenails", "diabetic foot", "foot ulcers", "plantar fasciitis",
            "anxiety", "insomnia", "phobias", "bipolar disorder", "OCD", "PTSD", "panic attacks", "hallucinations", "mood swings",
            "post-stroke recovery", "mobility issues", "muscle weakness", "injury rehab", "post-op recovery", "gait training", "chronic pain rehab",
            "lumps", "lipomas", "abscesses", "hemorrhoids", "surgical infections",
            "pneumonia", "kidney stones", "lung lesions", "stroke imaging", "abdominal masses",
            "palpitations", "high BP", "heart failure", "irregular heartbeat", "angina",
            "burning urination", "blood in urine", "frequent urination", "prostate issues", "urinary retention",
            "sprains", "stiffness", "musculoskeletal pain", "bone deformities", "osteoporosis",
            "pre-surgery assessment", "pain management", "spinal blocks", "nerve blocks", "anesthesia allergy history", "sedation monitoring",
            "obesity", "undernutrition", "diabetes diet", "cholesterol control", "pregnancy nutrition", "food allergies", "IBS-related diet",
            "migraine", "numbness", "tremors", "memory issues", "facial droop", "neuropathy", "vision loss",
            "muscle stiffness", "neck pain", "post-fracture recovery", "joint mobility",
            "difficulty dressing", "post-stroke daily tasks", "hand rehab", "motor skill deficits", "cognitive rehab",
            "speech delay", "stammering", "aphasia", "difficulty swallowing", "slurred speech", "voice issues", "lisps", "post-stroke speech",
            "chemotherapy", "infusion therapies", "minor procedures", "chronic disease management", "wound care",
            "brain tumors", "spine disorders", "hydrocephalus", "herniated discs", "chronic headaches", "spinal cord compression",
            "pregnancy", "menstrual issues", "infertility", "menopause", "PCOS", "pelvic pain", "fibroids", "prenatal care",
            "jaw fractures", "cysts", "impacted teeth", "TMJ disorders", "facial trauma",
            "bloating", "constipation", "acid reflux", "IBS", "Crohn's disease", "liver problems", "hepatitis",
            "acne", "eczema", "rashes", "skin infections", "hair loss", "fungal infections", "psoriasis", "warts", "pigmentation",
            "drug overdose", "poisoning", "alcohol toxicity", "industrial exposure", "chemical burns", "heavy metal toxicity",
            "thyroid disorders", "adrenal issues", "hormone imbalance", "hair thinning",
            "asthma", "COPD", "wheezing", "chronic cough", "lung infections", "TB",
            "edema", "high creatinine", "UTI", "dialysis",
            "jaundice in newborns", "low birth weight", "breathing issues", "feeding problems", "premature care",
            "performance issues", "overuse injuries", "rehab planning",
            "physical disability recovery", "long-term mobility issues", "brain injury rehab",
            "walking difficulty", "post-accident recovery",
            "cholesterol", "BMI check", "preventive check-ups", "vision", "hearing",
            "hearing test", "tinnitus evaluation", "hearing loss", "ear sensitivity",
            "varicose veins", "leg pain when walking", "poor circulation", "deep vein thrombosis", "ulcers",
            "chronic care", "diabetes monitoring", "vaccination"

        ]
        
        # If the query directly mentions a common symptom, it's likely a health problem query
        for symptom in common_symptoms:
            if symptom in query_lower:
                # Check for various patterns that indicate a health problem query
                if len(query_lower.split()) <= 5 or f"i have {symptom}" in query_lower:
                    logger.info(f"Detected health problem from symptom keyword: '{symptom}'")
                    return True
        
        # Check for explicit health specialist recommendation patterns
        recommendation_patterns = [
            r"(what|which).+(specialist|doctor).+(see|visit|consult).+for",
            r"(who|what).+(doctor|specialist).+(treat|help).+(with|for)",
            r"(should|could|can|must|need).+(see|visit|consult).+(specialist|doctor).+for",
            r"(health|medical).+(issue|problem|concern|condition)",
            r"i have.+(problem|issue|pain|ache|condition)",
            r"(suffering|suffer).+from",
            r"(experiencing|having|feel).+(pain|symptom)",
            r"(recommend|suggest).+(specialist|doctor)",
            r"(which|what).+(department|specialist)"
        ]
        
        for pattern in recommendation_patterns:
            if re.search(pattern, query_lower):
                return True
        
        # Check for health problem keywords alongside doctor/specialist terms
        health_problem_match = any(keyword in query_lower for keyword in self.health_problem_keywords)
        specialist_term_match = any(keyword in query_lower for keyword in self.specialty_keywords)
        
        # If both health problem and specialist terms are present, likely a health problem query
        if health_problem_match and specialist_term_match:
            return True
            
        # Additional common pattern: "I have X, what doctor should I see?"
        if re.search(r"(i have|i am|i'm|i've been).+what.+doctor", query_lower):
            return True
        
        # Check for specific health issue mention
        if re.search(r"(i have|suffering from|experiencing) [a-z\s]+(pain|ache|infection|issue|disease|problem)", query_lower):
            return True
            
        return False

    def _extract_health_problem(self, query: str) -> str:
        """
        Extract the health problem from the query.
        
        Args:
            query: The user query
            
        Returns:
            Extracted health problem or the original query if extraction fails
        """
        query_lower = query.lower()
        
        # Try to extract "I have X" pattern
        have_match = re.search(r"i have (.*?)(?:\.|$|\?|and|,|but|so)", query_lower)
        if have_match:
            health_problem = have_match.group(1).strip()
            logger.info(f"Extracted health problem from 'I have' pattern: '{health_problem}'")
            return health_problem
            
        # Try to extract "suffering from X" pattern
        suffering_match = re.search(r"suffering from (.*?)(?:\.|$|\?|and|,|but|so)", query_lower)
        if suffering_match:
            health_problem = suffering_match.group(1).strip()
            logger.info(f"Extracted health problem from 'suffering from' pattern: '{health_problem}'")
            return health_problem
            
        # Try to extract "experiencing X" pattern
        exp_match = re.search(r"experiencing (.*?)(?:\.|$|\?|and|,|but|so)", query_lower)
        if exp_match:
            health_problem = exp_match.group(1).strip()
            logger.info(f"Extracted health problem from 'experiencing' pattern: '{health_problem}'")
            return health_problem
            
        # If it's a short query that might be just the symptom, return as is
        if len(query_lower.split()) <= 5:
            logger.info(f"Using full short query as health problem: '{query_lower.strip()}'")
            return query_lower.strip()
            
        # If we can't extract anything specific, return the full query
        logger.info(f"Could not extract specific health problem, using full query: '{query}'")
        return query

    def _is_specialty_query(self, query: str) -> bool:
        """
        Determine if a query is related to doctor specialties.
        
        Args:
            query: The user query to check
            
        Returns:
            Boolean indicating if the query is about specialties
        """
        query_lower = query.lower()
        
        # Avoid false positives by checking for appointment terms first
        if any(word in query_lower for word in ["book", "schedule", "appointment", "slot", "visit", "time"]):
            return False
            
        # If it's a health problem query, don't classify as a general specialty query
        if self._is_health_problem_query(query_lower):
            return False
        
        # Check for follow-up queries about listing specialties
        full_list_patterns = [
            r"(yes|yeah|sure|ok|okay|full|complete|all).+(list|specialties|speciality|specialists)",
            r"(show|see|give).+(all|full|complete|more).+(list|specialties)",
            r"(list|show).+(all|everything|every|more)",
            r"what.+(all|else|other).+(available|have)",
            r"(show|see|list|get).+(more|additional|other|next).+(specialties|specialists)",
            r"(continue|next|rest).+(specialties|list)",
            r"show more specialt"
        ]
        
        for pattern in full_list_patterns:
            if re.search(pattern, query_lower):
                # This is a request for a full list, set a flag
                return True
        
        # Check for specialty keywords but make sure they're not just part of a general question
        specialty_keyword_match = False
        for keyword in self.specialty_keywords:
            if keyword in query_lower:
                specialty_keyword_match = True
                break
                
        if specialty_keyword_match:
            # Check if it's a question about specialties, not just containing the word
            specialty_patterns = [
                r"(do|does|are|can|have).+(doctor|specialist|physician)",
                r"(what|which).+(specialist|specialty|department)",
                r"looking for.+(doctor|specialist)",
                r"(find|need).+(doctor|specialist)",
                r"(tell|inform).+(about|your).+(specialist|specialty|department)",
                r"(what|which|any|is).+(doctor|specialist|department).+(available|have|for|treat)"
            ]
            
            for pattern in specialty_patterns:
                if re.search(pattern, query_lower):
                    return True
                    
            # If it's not an explicit question about specialties, be more conservative
            if not any(word in query_lower for word in ["what", "which", "where", "how", "when", "do", "does", "are", "can", "have", "tell", "inform"]):
                return False
        
        return specialty_keyword_match
    
    def _is_appointment_query(self, query: str) -> bool:
        """
        Determine if a query is related to appointments.
        
        Args:
            query: The user query to check
            
        Returns:
            Boolean indicating if the query is about appointments
        """
        query_lower = query.lower()
        
        # Direct appointment keywords that strongly indicate appointment intent
        strong_appointment_indicators = [
            "book appointment", "schedule appointment", "make appointment", 
            "get appointment", "create appointment", "new appointment",
            "appointment booking", "appointment schedule", "appointment availability",
            "book a visit", "schedule a visit", "book a slot"
        ]
        
        for indicator in strong_appointment_indicators:
            if indicator in query_lower:
                return True
        
        # Check for appointment-related question patterns
        appointment_patterns = [
            r"(book|schedule|make|get|create).+(appointment|visit|consultation)",
            r"(available|free|open).+(slot|time|appointment)",
            r"(when|how).+(see|meet|visit).+(doctor|specialist)",
            r"(my|check).+(appointment|booking|reservation)",
            r"walk.?in",
            r"follow.?up",
            r"(today|tomorrow).+(appointment|visit)",
            r"(appointment|booking).+(system|process|available)"
        ]
        
        for pattern in appointment_patterns:
            if re.search(pattern, query_lower):
                return True
                
        # More general queries that mention both appointment-related terms
        if any(word in query_lower for word in ["appointment", "booking", "schedule", "slot"]):
            if any(word in query_lower for word in ["doctor", "hospital", "clinic", "medical", "visit"]):
                return True
                
        return False
    
    def _select_appointment_tool(self, query: str) -> Dict[str, Any]:
        """
        Select the appropriate appointment tool based on the query.
        
        Args:
            query: The user query
            
        Returns:
            Dictionary containing the action type and parameters
        """
        query_lower = query.lower()
        
        # Check for specific appointment actions
        if any(term in query_lower for term in ["today", "current", "ongoing", "active"]):
            if "appointment" in query_lower:
                return {
                    "action_type": "get_today_appointments",
                    "parameters": {}
                }
            elif "visit" in query_lower:
                return {
                    "action_type": "get_ongoing_visits",
                    "parameters": {}
                }
        
        # Check for booking-related queries
        if any(term in query_lower for term in ["book", "schedule", "make", "create", "get", "new"]):
            if any(term in query_lower for term in ["walk in", "walk-in", "walkin"]):
                return {
                    "action_type": "create_walkin",
                    "parameters": {
                        "resource_id": "2",
                        "session_id": "363",
                        "session_date": "2025-06-25",
                        "from_time": "07%3A10%3A00",
                        "patient_id": "3598"
                    }
                }
            else:
                # For general booking, we need to get slots first
                return {
                    "action_type": "get_session_slots",
                    "parameters": {
                        "resource_id": "2",
                        "session_date": "2025-06-25",
                        "session_id": "363"
                    }
                }
                
        if any(term in query_lower for term in ["follow up", "follow-up", "followup"]):
            return {
                "action_type": "get_appointment_followup",
                "parameters": {
                    "patient_id": "3598",
                    "date_from": "2025-06-25",
                    "date_to": "2025-06-25"
                }
            }
            
        if any(term in query_lower for term in ["journey", "status", "progress"]):
            return {
                "action_type": "get_patient_journey",
                "parameters": {
                    "visit_id": "3502"
                }
            }
            
        if any(term in query_lower for term in ["available", "slot", "time"]):
            return {
                "action_type": "get_session_slots",
                "parameters": {
                    "resource_id": "2",
                    "session_date": "2025-06-25",
                    "session_id": "363"
                }
            }
            
        # Default to initialization if no specific query is matched
        return {
            "action_type": "init_appointments",
            "parameters": {}
        }
    
    def _act(self, action: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the chosen action based on reasoning.
        
        Args:
            action: The action to execute, including action type and parameters
            
        Returns:
            Result of the action execution
        """
        logger.info("\n=== ACTION ===")
        
        if not action or not isinstance(action, dict):
            logger.error("Invalid action format")
            return {"success": False, "error": "Invalid action format"}
        
        # Extract action type and parameters
        action_type = action.get("action_type")
        parameters = action.get("parameters", {})
        
        if not action_type:
            logger.error("No action type specified")
            return {"success": False, "error": "No action type specified"}
            
        # Log the action
        logger.info(f"Executing action: {action_type}")
        logger.info(f"Parameters: {json.dumps(parameters, indent=2)}")
        
        # If this is a get_user_dataset action for doctor availability, use current date only
        if action_type == "get_user_dataset" and parameters.get("query_name") == "APPTFINDRESO_AI":
            # Get current date in YYYY-MM-DD format (Singapore timezone)
            current_date = pendulum.now("Asia/Singapore").format("YYYY-MM-DD")
            
            # Use current date only to ensure slots shown are actually available today
            if not parameters.get("date_from"):
                parameters["date_from"] = current_date
            if not parameters.get("date_to"):
                parameters["date_to"] = current_date
            
            logger.info(f"Using current date only for doctor search: {parameters['date_from']} to {parameters['date_to']}")
        
        # Check if we have a tool for this action
        if action_type in self.tools:
            try:
                # Call the corresponding tool with the parameters
                result = self.tools[action_type](parameters)
                
                # Add action_type to the result for easier tracking in _observe
                if isinstance(result, dict) and "action_type" not in result:
                    result["action_type"] = action_type
                
                # Special handling for recommend_specialist to store specialty IDs immediately
                if action_type == "recommend_specialist" and isinstance(result, dict):
                    filtered_suggestions = result.get("filtered_suggestions", [])
                    for suggestion in filtered_suggestions:
                        specialty_name = suggestion.get("specialty")
                        specialty_id = suggestion.get("specialty_id")
                        if specialty_name and specialty_id:
                            self._store_specialty_in_memory(specialty_name, specialty_id)
                
                logger.info(f"Action completed successfully")
                return result
            except Exception as e:
                error_msg = f"Error executing action {action_type}: {str(e)}"
                logger.error(error_msg)
                traceback_info = traceback.format_exc()
                logger.error(traceback_info)
                return {"success": False, "error": error_msg, "action_type": action_type}
        else:
            error_msg = f"Unknown action type: {action_type}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    def _observe(self, action_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process and interpret the result of an action.
        
        Args:
            action_result: Result from the action step
            
        Returns:
            Processed observation
        """
        logger.info("\n=== OBSERVATION ===")
        
        # Check for errors
        if "error" in action_result:
            error_message = action_result.get("error", "Unknown error")
            logger.error(f"Error in action: {error_message}")
            return {
                "observation": {
                    "error": error_message,
                    "message": f"I encountered an error: {error_message}. Please try again or ask a different question."
                }
            }
        
        # Process different action results based on type
        action_type = action_result.get("action_type", "unknown")
        
        # Handle doctor specialties
        if action_type == "get_doctor_specialties":
            specialties = action_result.get("specialties", [])
            is_full_list = action_result.get("is_full_list", False)
            
            # Extract specialty information
            specialty_names = []
            specialty_count = 0
            
            for specialty in specialties:
                desc = specialty.get("DESCRIPTION", "")
                if desc:
                    specialty_names.append(desc)
                    specialty_count += 1
                    
                    # Store in memory for future reference
                    specialty_id = specialty.get("IDENTIFIER", "")
                    if specialty_id:
                        self._store_specialty_mapping(desc, str(specialty_id))
            
            # Sort for better presentation
            specialty_names.sort()
            
            logger.info(f"Found {specialty_count} specialties")
            
            if specialty_count == 0:
                return {
                    "observation": {
                        "message": "I couldn't find any specialties matching your query.",
                        "type": "specialty_list",
                        "specialty_count": 0,
                        "formatted_list": []
                    }
                }
            
            if is_full_list:
                logger.info("Returning full list of specialties")
                return {
                    "observation": {
                        "message": f"Here's the complete list of {specialty_count} specialties available at our hospital.",
                        "type": "specialty_list",
                        "specialty_count": specialty_count,
                        "formatted_list": specialty_names
                    }
                }
            else:
                # For queries about specific specialties
                logger.info(f"Returning filtered list of {specialty_count} specialties")
                return {
                    "observation": {
                        "message": f"I found {specialty_count} specialty/specialties that might match what you're looking for.",
                        "type": "specialty_list",
                        "specialty_count": specialty_count,
                        "formatted_list": specialty_names
                    }
                }
                
        # Handle specialist recommendations
        elif action_type == "recommend_specialist":
            filtered_suggestions = action_result.get("filtered_suggestions", [])
            original_suggestions = action_result.get("original_suggestions", [])
            emergency_advice = action_result.get("emergency_advice", "")
            health_problem = action_result.get("health_problem", "Unknown health issue")
            
            # Format the response
            result_message = f"Specialist recommendations for '{health_problem}':"
            
            # Store all filtered suggestions in memory for future reference
            if filtered_suggestions:
                logger.info(f"Hospital available recommendations:")
                for suggestion in filtered_suggestions:
                    specialty_name = suggestion.get("specialty", "Unknown specialty")
                    specialty_id = suggestion.get("specialty_id")  # Get specialty_id from the suggestion if available
                    reason = suggestion.get("reason", "No reason provided")
                    logger.info(f"- {specialty_name}: {reason}")
                    
                    # Store in memory if not already there
                    if specialty_id:
                        logger.info(f"Storing specialty '{specialty_name}' with ID '{specialty_id}' in memory")
                        self._store_specialty_in_memory(specialty_name, specialty_id)
                    else:
                        logger.info(f"No ID found for specialty '{specialty_name}' in filtered suggestion")
                        self._store_specialty_in_memory(specialty_name)
                
                # Log the total number of specialties found
                logger.info(f"Found {len(filtered_suggestions)} matching specialties for '{health_problem}'")
                
                return {
                    "observation": {
                        "message": result_message,
                        "filtered_suggestions": filtered_suggestions,
                        "original_suggestions": original_suggestions,
                        "emergency_advice": emergency_advice,
                        "health_problem": health_problem,
                        "has_matches": True
                    }
                }
            elif original_suggestions:
                # If we have original suggestions but no matches, still store for reference
                # but inform that these may not be available at the hospital
                logger.info("No exact specialty matches at hospital, but have non-hospital suggestions")
                for suggestion in original_suggestions:
                    specialty = suggestion.get("specialty", "Unknown specialty")
                    reason = suggestion.get("reason", "No reason provided")
                    logger.info(f"- {specialty} (not in hospital): {reason}")
                    
                    # For non-filtered suggestions, we store them with a null ID to indicate they're not available
                    self._store_specialty_in_memory(specialty, None)
                
                return {
                    "observation": {
                        "message": f"No direct specialty matches for '{health_problem}', but have general recommendations.",
                        "filtered_suggestions": [],
                        "original_suggestions": original_suggestions,
                        "emergency_advice": emergency_advice,
                        "health_problem": health_problem,
                        "has_matches": False
                    }
                }
            else:
                # No suggestions at all
                logger.info("No specialty suggestions found")
                return {
                    "observation": {
                        "message": f"Unable to determine appropriate specialist for '{health_problem}'.",
                        "filtered_suggestions": [],
                        "original_suggestions": [],
                        "emergency_advice": emergency_advice,
                        "health_problem": health_problem,
                        "has_matches": False
                    }
                }
        
        # Handle doctor availability
        elif action_type == "get_user_dataset":
            # Format doctor availability data
            if "available_doctors" in action_result and "specialty_name" in action_result:
                doctors = action_result.get("available_doctors", [])
                doctor_count = action_result.get("doctor_count", 0)
                specialty_name = action_result.get("specialty_name", "Unknown Specialty")
                
                if doctor_count > 0 and doctors:
                    # Record the specialty for future reference
                    self.last_recommended_specialty = specialty_name
                    
                    # Ensure the specialty is in our memory
                    if specialty_name not in self.specialty_memory and "specialty_id" in action_result:
                        self._store_specialty_mapping(specialty_name, action_result.get("specialty_id"))
                
                # Log doctor information
                logger.info(f"Doctor availability for {specialty_name}: {doctor_count} doctors found")
                
                if doctor_count > 0:
                    for i, session in enumerate(doctors):  # Each entry is now a session, not a doctor
                        name = session.get("name", "Unknown Doctor")
                        available_slots = session.get("available_slots", 0)  # This is now an integer
                        session_desc = session.get("session_desc", "No description")
                        start_time = session.get("start_time_display", "Unknown time")
                        session_date = session.get("session_date", "Unknown date")
                        
                        logger.info(f"Session {i+1}: {name} - {session_desc} ({start_time}) on {session_date}: {available_slots} slots available")
                
                return {
                    "observation": {
                        "message": f"Found doctor availability for {specialty_name}",
                        "type": "doctor_availability",
                        "specialty_name": specialty_name,
                        "date_range": action_result.get("date_range", "Unknown dates"),
                        "available_doctors": doctors,
                        "doctor_count": doctor_count
                    }
                }
            else:
                logger.info("Invalid or missing doctor availability data")
                return {
                    "observation": {
                        "message": "I couldn't retrieve doctor availability information. Please try again or ask about a different specialty.",
                        "error": "Invalid doctor availability data"
                    }
                }
                
        # Generic handling for other actions
        else:
            # For any other action types, return the raw action result
            logger.info(f"Generic action result for {action_type}")
            return {
                "observation": action_result
            }
    
    def _store_specialty_in_memory(self, specialty_name: str, specialty_id: str = None) -> None:
        """
        Stores a specialty name in memory for future reference.
        If the specialty already exists in memory, does nothing.
        
        Args:
            specialty_name: The name of the specialty
            specialty_id: Optional specialty ID, if available
        """
        # Skip if None or empty string
        if not specialty_name:
            return
        
        # Initialize if not already
        if not hasattr(self, 'specialty_memory'):
            self.specialty_memory = {}
        
        # Check if we already have this specialty
        if specialty_name in self.specialty_memory:
            logger.info(f"Specialty '{specialty_name}' already in memory with ID: {self.specialty_memory[specialty_name]}")
            return
        
        # If specialty_id is directly provided, use it
        if specialty_id:
            self.specialty_memory[specialty_name] = specialty_id
            logger.info(f"Stored specialty '{specialty_name}' with ID '{specialty_id}' in memory")
            return
            
        # Otherwise, try to find it in our specialty_id_mapping
        if hasattr(self, 'specialty_id_mapping') and specialty_name in self.specialty_id_mapping:
            specialty_id = self.specialty_id_mapping[specialty_name]
            self.specialty_memory[specialty_name] = specialty_id
            logger.info(f"Stored specialty '{specialty_name}' with ID '{specialty_id}' in memory")
            return
            
        # Finally, check if a case-insensitive match exists
        for name, id_value in self.specialty_id_mapping.items() if hasattr(self, 'specialty_id_mapping') else {}:
            if name.lower() == specialty_name.lower():
                self.specialty_memory[specialty_name] = id_value
                logger.info(f"Stored specialty '{specialty_name}' with ID '{id_value}' in memory (case-insensitive match)")
                return
        
        # If we get here, we couldn't find the ID - this is not ideal but we'll log a warning
        logger.warning(f"No ID found for specialty '{specialty_name}'. This may cause issues later.")
        # Still store the name so we can at least detect it in user queries
        self.specialty_memory[specialty_name] = None
    
    def _get_specialty_name_from_id(self, specialty_id: str) -> str:
        """
        Get specialty name from ID.
        
        Args:
            specialty_id: The ID of the specialty
            
        Returns:
            The name of the specialty
        """
        # First check memory for the specialty name
        if hasattr(self, 'specialty_memory'):
            for name, id_val in self.specialty_memory.items():
                if id_val == specialty_id:
                    return name
        
        # If not found in memory, query the API
        specialties_data = self.tools_manager.get_doctor_specialties({})
        specialties = specialties_data.get("specialties", [])
        
        for specialty in specialties:
            if str(specialty.get("IDENTIFIER")) == specialty_id:
                return specialty.get("DESCRIPTION", "Unknown Specialty")
                
        return "Unknown Specialty"
    
    def _extract_specialty_from_history(self) -> str:
        """
        Extract specialty ID from conversation history.
        
        Returns:
            Specialty ID if found, "1" otherwise as a fallback
        """
        try:
            # Check if we have specialty memory
            if not hasattr(self, 'specialty_memory') or not self.specialty_memory:
                logger.warning("No specialty memory found, this should not happen during doctor availability requests")
                return "1"  # Default to ID 1 if no specialty is found
            
            # Track the most recently mentioned specialty in conversation
            most_recent_specialty = None
            most_recent_specialty_id = None
                
            # Check the last few messages in conversation history to find mentions of specialties
            last_messages_to_check = min(5, len(self.conversation_history))
            for i in range(1, last_messages_to_check + 1):
                if len(self.conversation_history) >= i:
                    last_msg = self.conversation_history[-i]["content"].upper()
                    
                    # Look for specialty mentions in the message
                    for specialty_name, specialty_id in self.specialty_memory.items():
                        if specialty_name.upper() in last_msg:
                            logger.info(f"Found specialty '{specialty_name}' mentioned in conversation with ID '{specialty_id}'")
                            # If this is the first specialty found, or it's in a more recent message than previous finds
                            if most_recent_specialty is None:
                                most_recent_specialty = specialty_name
                                most_recent_specialty_id = specialty_id
                                # Don't break, continue checking this message for other specialties
            
            # If we found a specialty in conversation history, use that
            if most_recent_specialty_id:
                logger.info(f"Using most recently mentioned specialty: '{most_recent_specialty}' with ID '{most_recent_specialty_id}'")
                return most_recent_specialty_id
            
            # If we only have one specialty in memory, use that
            if len(self.specialty_memory) == 1:
                specialty_name = list(self.specialty_memory.keys())[0]
                specialty_id = list(self.specialty_memory.values())[0]
                logger.info(f"Using single specialty '{specialty_name}' with ID '{specialty_id}'")
                return specialty_id
            
            # Look for the most recently stored specialty
            if hasattr(self, 'last_recommended_specialty') and self.last_recommended_specialty in self.specialty_memory:
                specialty_id = self.specialty_memory[self.last_recommended_specialty]
                logger.info(f"Using last recommended specialty '{self.last_recommended_specialty}' with ID '{specialty_id}'")
                return specialty_id
            
            # If no specialty is found in conversation, return the first specialty in memory
            if self.specialty_memory:
                first_specialty = next(iter(self.specialty_memory.items()))
                specialty_name, specialty_id = first_specialty
                logger.info(f"Using first specialty in memory: '{specialty_name}' with ID '{specialty_id}'")
                return specialty_id
        except Exception as e:
            logger.error(f"Error extracting specialty from history: {str(e)}")
            
        # Default fallback    
        logger.info("No specialties found in memory or error occurred, using default ID 1")
        return "1"
    
    def chat(self, user_query: str, session_id: Optional[str] = None) -> str:
        """
        Process a user query through the ReAct (Reason-Act-Observe) flow.
        
        Args:
            user_query: The user's query string
            session_id: Optional session identifier for persistence
            
        Returns:
            Response to the user
        """
        # Initialize or resume session
        if session_id and session_id != self.current_session_id:
            self.initialize_session(session_id)
        elif not self.current_session_id:
            self.initialize_session()
        
        # Always ensure we're using the current date for any date-related queries (Singapore timezone)
        current_date = pendulum.now("Asia/Singapore").format("YYYY-MM-DD")
        
        # Add user query to conversation history
        self.conversation_history.append({"role": "user", "content": user_query})
        
        # Update session activity in database
        if self.db.is_connected() and self.current_session_id:
            self.db.update_session_activity(self.current_session_id)
            
        # Check if this is a health problem query
        if self._is_health_problem_query(user_query):
            health_problem = self._extract_health_problem(user_query)
            self._store_health_problem_memory(health_problem)
        
        # Check if this is a specialty selection
        if self._is_specialty_selection_query(user_query):
            selected_specialty = self._extract_selected_specialty(user_query)
            if selected_specialty:
                logger.info(f"User selected specialty: {selected_specialty}")
                
                # Store in memory
                self._store_specialty_in_memory(selected_specialty)
                
                # Override user_query to ask about doctor availability for this specialty
                user_query = f"Show me available {selected_specialty} doctors for today"
        
        # ===== REASON =====
        # First, reason about the query to decide on a course of action
        reasoning = self._reason(user_query)
        
        # If we can provide a direct answer without using a tool
        if not reasoning.get("use_tool", False):
            direct_answer = reasoning.get("direct_answer", "I'm not sure how to help with that.")
            
            # Add response to conversation history
            self.conversation_history.append({"role": "assistant", "content": direct_answer})
            
            # Return the direct answer
            return direct_answer
            
        # ===== ACT =====
        # Execute the action determined by reasoning
        action_result = self._act(reasoning.get("action", {}))
        
        # Store specialty info if present in the action result
        if "action_type" in action_result and action_result["action_type"] == "recommend_specialist":
            filtered_suggestions = action_result.get("filtered_suggestions", [])
            for suggestion in filtered_suggestions:
                specialty_name = suggestion.get("specialty")
                specialty_id = suggestion.get("specialty_id")
                if specialty_name and specialty_id:
                    self._store_specialty_memory(specialty_name, specialty_id)
        
        # ===== OBSERVE =====
        # Process and interpret the results of the action
        observation = self._observe(action_result)
        
        # ===== DECIDE RESPONSE =====
        # Special case for doctor availability - use direct formatting to ensure all doctors are shown
        obs_data = observation.get("observation", {})
        if isinstance(obs_data, dict) and obs_data.get("type") == "doctor_availability":
            doctors = obs_data.get("available_doctors", [])
            doctor_count = obs_data.get("doctor_count", 0)
            specialty_name = obs_data.get("specialty_name", "Unknown Specialty")
            date_range = obs_data.get("date_range", "Unknown dates")
            
            # If specialty name is 'Unknown Specialty', try to get it from action_result or reasoning
            if specialty_name == "Unknown Specialty":
                if "action" in reasoning and "parameters" in reasoning["action"]:
                    specialty_id = reasoning["action"]["parameters"].get("specialty_id")
                    if specialty_id:
                        # Try to find the specialty name from the ID
                        for name, id_val in self.specialty_memory.items():
                            if id_val == specialty_id:
                                specialty_name = name
                                logger.info(f"Found specialty name '{specialty_name}' from ID '{specialty_id}'")
                                break
            
            if doctor_count > 0 and doctors:
                # Format available doctors
                response = f"Here are the available {specialty_name} doctors:"
                
                for i, session in enumerate(doctors):  # Each entry is now a session, not a doctor
                    name = session.get("name", "Unknown Doctor")
                    resource_id = session.get("resource_id", "")
                    session_id = session.get("session_id", "")
                    available_slots = session.get("available_slots", 0)  # This is now an integer
                    start_time = session.get("start_time_display", "Unknown time")
                    session_desc = session.get("session_desc", "")
                    session_date = session.get("session_date", "")
                    
                    response += f"\n\n• **Dr. {name}**"
                    response += f"\n  {start_time}"
                    response += f"\n  {available_slots} slots available"
                    response += f"\n  RESOURCEID: {resource_id}"
                    response += f"\n  SESSIONID: {session_id}"
                    response += f"\n  SESSIONDATE: {session_date}"
                
                # Store memory about doctor availability
                self._store_memory(
                    "doctor_availability",
                    f"Showed doctor availability for {specialty_name}, found {doctor_count} doctors",
                    3,
                    {"specialty_name": specialty_name, "doctor_count": doctor_count}
                )
                
                # Add response to conversation history
                self.conversation_history.append({"role": "assistant", "content": response})
                
                # Log and return the direct response
                logger.info(f"Sent WebSocket response: {response[:50]}...")
                return response
            else:
                # No doctors available
                # Extract date from date_range or action parameters
                requested_date = None
                if "action" in reasoning and "parameters" in reasoning["action"]:
                    requested_date = reasoning["action"]["parameters"].get("date_from")
                
                if requested_date:
                    # Format the date for display
                    try:
                        date_obj = pendulum.parse(requested_date)
                        formatted_date = date_obj.format("MMMM D, YYYY")
                    except Exception:
                        formatted_date = requested_date
                else:
                    # Fallback to date_range from observation
                    formatted_date = date_range
                
                response = f"I'm sorry, but we couldn't find any {specialty_name} doctors available for {formatted_date}. Would you like to try a different date or specialty?"
                
                # Add response to conversation history
                self.conversation_history.append({"role": "assistant", "content": response})
                
                # Log and return the direct response
                logger.info(f"Sent WebSocket response: {response[:50]}...")
                return response
        
        # For other cases, use the normal LLM-based response generation
        response = self._decide_response(user_query, reasoning, action_result, observation)
        
        # Add response to conversation history
        self.conversation_history.append({"role": "assistant", "content": response})
        
        return response
    
    def _construct_reasoning_prompt(self, user_query: str) -> List[Dict[str, str]]:
        """
        Constructs the prompt for the reasoning step.
        
        Args:
            user_query: User's input query
            
        Returns:
            Formatted prompt for the LLM
        """
        system_message = """
        You are an intelligent hospital assistant that helps users with their queries about doctor specialties, health problems, and appointments.
        
        Your task is to analyze the user's query and decide whether to use a tool or answer directly.
        
        Currently, you have access to the following tools:
        
        SPECIALTY TOOLS:
        1. get_doctor_specialties: Retrieves information about doctor specialties
        2. recommend_specialist: Recommends the appropriate specialist based on a health problem, symptom, or medical condition
        
        APPOINTMENT TOOLS:
        3. activate_sso: Activates Single Sign-On for a user
        4. search_by_id_number: Searches for a patient by ID number
        5. get_today_appointments: Retrieves today's appointments
        6. get_ongoing_visits: Retrieves ongoing patient visits
        7. init_appointments: Initializes the appointments system
        8. get_user_dataset: Retrieves user dataset for appointments
        9. get_session_slots: Retrieves appointment session slots
        10. create_walkin: Creates a walk-in appointment
        11. get_appointment_number: Retrieves appointment numbers
        12. create_visit: Creates a patient visit
        13. get_patient_journey: Retrieves patient journey information
        14. get_appointment_followup: Retrieves follow-up appointment information
        
        IMPORTANT: You should ONLY answer questions about doctor specialties, health issues requiring specialists, and appointments in the hospital. For ANY other topic:
        1. You must set "out_of_scope" to true
        2. You should NOT provide an answer, as you don't have verified information on other topics
        3. Your response should direct the user back to asking about doctor specialties, health issues, or appointments
        
        EXAMPLES OF SPECIALTY QUERIES:
        - "What specialties do you have?"
        - "Do you have cardiologists?"
        - "Tell me about your orthopedic department"
        
        EXAMPLES OF HEALTH PROBLEM QUERIES:
        - "I have chest pain, which doctor should I see?"
        - "What specialist treats frequent headaches?"
        - "Who should I consult for joint pain?"
        - "I'm experiencing shortness of breath"
        - "Which department handles skin rashes?"
        
        EXAMPLES OF APPOINTMENT QUERIES:
        - "I want to book an appointment"
        - "Show me today's appointments"
        - "What appointment slots are available?"
        - "How do I schedule a follow-up?"
        
        EXAMPLES OF OUT-OF-SCOPE QUERIES:
        - "What medications should I take for headaches?"
        - "How much does surgery cost?"
        - "What's the weather today?"
        
        For each query, you should:
        1. Think about what the user is asking for
        2. Decide if it's about doctor specialties or appointments (if not, mark it out of scope)
        3. Format your response as JSON with the following structure:
        
        If you need to use a tool:
        {
            "reasoning": "your step-by-step reasoning",
            "use_tool": true,
            "action": {
                "action_type": "[one of the tool names]",
                "parameters": {"param1": "value1", "param2": "value2"}
            }
        }
        
        If you can answer directly (ONLY for basic specialty/appointment information or greetings):
        {
            "reasoning": "your step-by-step reasoning",
            "use_tool": false,
            "direct_answer": "your answer to the user's query about doctor specialties or appointments"
        }
        
        If the query is NOT about doctor specialties or appointments:
        {
            "reasoning": "your step-by-step reasoning",
            "use_tool": false,
            "out_of_scope": true,
            "direct_answer": null
        }
        """
        
        messages = [
            {"role": "system", "content": system_message},
        ]
        
        # Add conversation history (limited to last few exchanges for context)
        for message in self.conversation_history[-4:]:
            messages.append(message)
            
        # Add the current query
        messages.append({"role": "user", "content": f"User query: {user_query}\n\nRespond with the JSON format described in the instructions."})
        
        return messages
    
    def _construct_final_answer_prompt(self, user_query: str, reasoning: Dict[str, Any], 
                                      action_result: Dict[str, Any], observation: Dict[str, Any]) -> List[Dict[str, str]]:
        """
        Constructs the prompt for generating the final answer.
        
        Args:
            user_query: Original user query
            reasoning: Output from the reasoning step
            action_result: Result from the action step
            observation: Processed observation
            
        Returns:
            Formatted prompt for the LLM
        """
        # Get observation data
        obs_data = observation.get("observation", {})
        
        # Check for different response types
        is_doctor_availability = isinstance(obs_data, dict) and obs_data.get("type") == "doctor_availability"
        is_specialty_list = isinstance(obs_data, dict) and obs_data.get("type") == "specialty_list"
        
        if is_specialty_list:
            # Special handling for listing specialties
            system_message = """
            You are Athena, the official virtual assistant for SmartClinic Hospital. You speak on behalf of the hospital in a friendly, professional, and helpful manner.
            
            RESPONSE RULES:
            1. Be clear and organized when presenting specialty lists
            2. Always use "we" and "our" to represent the hospital
            3. Only include specialties from the provided list
            4. Format your response for readability
            
            FOR SPECIALTY LISTS:
            1. Start with a brief opening: "Here are the medical specialties available at our hospital:"
            2. Present the list in an organized manner using bullet points (•)
            3. Group specialties if there are more than 15
            4. End with: "Would you like me to help you find a specialist for a specific health concern?"
            """
            
            specialty_names = obs_data.get("formatted_list", [])
            
            prompt_content = f"""
            User query: {user_query}
            
            The user has requested a list of all available specialties. 
            
            Observation contains a list of {len(specialty_names)} specialty names: 
            {specialty_names}
            
            Create a well-formatted response that lists all the specialties available at the hospital.
            If there are many specialties (over 15), group them into columns or categories for better readability.
            End by asking if they would like help finding a specialist for a specific health concern.
            """
            
        elif is_doctor_availability:
            system_message = """
            You are Athena, the official virtual assistant for SmartClinic Hospital. You speak on behalf of the hospital in a friendly, professional, and helpful manner.
            
            RESPONSE RULES:
            1. Be extremely concise and direct - aim for 2-3 sentences total
            2. Always use "we" and "our" to represent the hospital
            3. Only respond with information from the observation - never hallucinate details
            4. Keep messages short yet precise
            
            FOR DOCTOR AVAILABILITY RESPONSES:
            1. If doctors are available:
               a. Start by acknowledging the specialty: "Here are the available [SPECIALTY] doctors from [DATE RANGE]:"
               b. List each doctor with their availability: "• Dr. [NAME]: [NUMBER] sessions with [TOTAL] total available slots"
               c. For EACH doctor, show their session details: "Sessions: [DATE] [TIME] ([SESSION_DESC]): [NUMBER] slots available"
               d. Provide clear booking instructions: "Would you like to schedule an appointment with any of these doctors? Please let me know which doctor you prefer."
            
            2. If NO doctors are available:
               a. Clearly state: "I'm sorry, but we couldn't find any [SPECIALTY] doctors available from [DATE RANGE]."
               b. Offer alternatives: "Would you like to try a different specialty or time range?"
               c. NEVER make up or hallucinate doctor names or availability when none are found.
            
            STRUCTURE FOR DOCTOR AVAILABILITY (when doctors are available):
            "Here are the available [SPECIALTY] doctors from [DATE RANGE]:
            
            • Dr. [NAME 1]: [NUMBER] sessions with [TOTAL] total available slots
              Sessions:
              - [DATE] [TIME] ([SESSION_DESC]): [NUMBER] slots available
              - [DATE] [TIME] ([SESSION_DESC]): [NUMBER] slots available
              [up to 3 sessions]
            
            • Dr. [NAME 2]: [NUMBER] sessions with [TOTAL] total available slots
              Sessions:
              - [DATE] [TIME] ([SESSION_DESC]): [NUMBER] slots available
              - [DATE] [TIME] ([SESSION_DESC]): [NUMBER] slots available
              [up to 3 sessions]
            [up to 5 doctors]
            
            Would you like to schedule an appointment with any of these doctors? Please let me know which doctor you prefer."
            
            STRUCTURE FOR NO DOCTORS AVAILABLE:
            "I'm sorry, but we couldn't find any [SPECIALTY] doctors available from [DATE RANGE]. Would you like to try a different specialty or time range?"
            """
            
            doctors = obs_data.get('available_doctors', [])
            doctor_count = obs_data.get('doctor_count', 0)
            
            prompt_content = f"""
            User query: {user_query}
            
            Tool used: {reasoning.get('action', {}).get('action_type', 'No tool used')}
            
            Observation contains doctor availability information with the following details:
            - Specialty: {obs_data.get('specialty_name', 'Unknown Specialty')}
            - Date Range: {obs_data.get('date_range', 'Unknown dates')}
            - Number of doctors: {doctor_count}
            """
            
            if doctor_count > 0 and doctors:
                prompt_content += f"""
            - Available doctors: 
            {json.dumps(doctors[:3], indent=2)}
            
            Create a concise response listing up to 5 doctors with their availability and session details for the first doctor. Prompt the user to choose one for appointment scheduling.
            """
            else:
                prompt_content += """
            - Available doctors: []
            
            Create a concise response indicating that no doctors are available for this specialty in the given date range. Suggest trying a different specialty or time range.
            DO NOT make up or hallucinate any doctor names or availability information.
            """
        else:
            system_message = """
            You are Athena, the official virtual assistant for SmartClinic Hospital. You speak on behalf of the hospital in a friendly, professional, and helpful manner.
            
            RESPONSE RULES:
            1. Be extremely concise and direct - aim for 2-3 sentences total
            2. Always use "we" and "our" to represent the hospital
            3. Only respond with information from the observation - never hallucinate details
            4. Format any emergency advice with ⚠️ and make it stand out
            5. Keep messages short yet precise
            
            FOR SPECIALIST RECOMMENDATIONS:
            1. Acknowledge the health problem: "For [health problem]..."
            2. Present ALL available filtered specialties: "We recommend the following specialties:"
            3. List each specialty with its reason: "• [SPECIALTY]: [REASON]"
            4. Call to action: ALWAYS ask the user to choose: "Which specialty would you prefer to explore? Please type the name of your preferred specialty to see available doctors."
            5. If emergency advice exists, make it a separate statement prefixed with ⚠️
            6. NEVER suggest options that aren't available at the hospital
            
            STRUCTURE FOR SPECIALIST RESPONSES WITH MULTIPLE OPTIONS:
            "For [health problem], we recommend the following specialties:
            
            • [SPECIALTY 1]: [REASON 1]
            • [SPECIALTY 2]: [REASON 2]
            
            Which specialty would you prefer to explore? Please type the name of your preferred specialty to see available doctors.
            
            [Only if applicable: ⚠️ [EMERGENCY ADVICE]]"
            
            STRUCTURE FOR SPECIALIST RESPONSES WITH SINGLE OPTION:
            "For [health problem], we recommend seeing our [SPECIALTY] specialist. [REASON]
            
            Would you like to see available [SPECIALTY] doctors for an appointment? Reply with 'yes' to view doctors.
            
            [Only if applicable: ⚠️ [EMERGENCY ADVICE]]"
            
            FOR OTHER QUERIES:
            Be equally concise and direct, focusing only on the most essential information. Avoid unnecessary explanations or pleasantries.
            
            Provide only the final answer without mentioning your reasoning process or that you used tools.
            """
            
            prompt_content = f"""
            User query: {user_query}
            
            Reasoning: {reasoning.get('reasoning', 'No reasoning provided')}
            
            Tool used: {reasoning.get('action', {}).get('action_type', 'No tool used')}
            Tool parameters: {json.dumps(reasoning.get('action', {}).get('parameters', {}), indent=2)}
            
            Observation: {json.dumps(observation.get('observation', 'No observation available'), indent=2)}
            
            Create an extremely concise response (2-3 sentences) to the user's query as Athena, SmartClinic Hospital's virtual assistant.
            
            If this is a specialist recommendation and there are multiple filtered_suggestions, be sure to list ALL available options and ask the user to choose one by typing the specialty name.
            """
        
        messages = [
            {"role": "system", "content": system_message},
            {"role": "user", "content": prompt_content}
        ]
        
        return messages
    
    def _call_llm(self, messages: List[Dict[str, str]]) -> Dict[str, Any]:
        """
        Call the local LLM with the given prompt.
        
        Args:
            messages: List of message dictionaries for the LLM
            
        Returns:
            LLM response
        """
        headers = {
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": "local-model", # This can be adjusted based on the LLM's requirements
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 800
        }
        
        try:
            response = requests.post(
                self.llm_endpoint,
                headers=headers,
                json=payload
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Error calling LLM: {str(e)}")
            raise Exception(f"LLM call failed: {str(e)}")
    
    def _parse_reasoning_response(self, llm_response: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse the LLM's response to extract reasoning and action information.
        
        Args:
            llm_response: Raw response from the LLM
            
        Returns:
            Parsed reasoning output
        """
        try:
            # Extract the content from the LLM response
            content = llm_response.get("choices", [{}])[0].get("message", {}).get("content", "{}")
            
            # Find JSON in the content (in case the LLM added extra text)
            import re
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                content = json_match.group(0)
            
                            # Parse the JSON response
                parsed_response = json.loads(content)
                
                # Ensure required fields are present
                if "reasoning" not in parsed_response:
                    parsed_response["reasoning"] = "No explicit reasoning provided by LLM"
                    
                # Make sure recommend_specialist action always has health_problem parameter
                if parsed_response.get("use_tool") and parsed_response.get("action", {}).get("action_type") == "recommend_specialist":
                    action_params = parsed_response.get("action", {}).get("parameters", {})
                    if not action_params.get("health_problem"):
                        # Try to extract health problem or fallback to user query
                        health_problem = self._extract_health_problem(user_query)
                        parsed_response["action"]["parameters"] = {"health_problem": health_problem}
                        logger.info(f"Added missing health_problem parameter: '{health_problem}'")
                
            return parsed_response
        except Exception as e:
            logger.error(f"Error parsing LLM response: {str(e)}")
            return {
                "reasoning": "Failed to parse LLM response",
                "use_tool": False,
                "out_of_scope": True,
                "direct_answer": "I'm having trouble processing your question. Could you please ask specifically about doctor specialties or appointments available at our hospital?"
            }
    
    def _extract_final_answer(self, llm_response: Dict[str, Any]) -> str:
        """
        Extract the final answer from the LLM's response.
        
        Args:
            llm_response: Raw response from the LLM
            
        Returns:
            Extracted final answer as a string
        """
        try:
            return llm_response.get("choices", [{}])[0].get("message", {}).get("content", "")
        except Exception as e:
            logger.error(f"Error extracting final answer: {str(e)}")
            return "I'm sorry, I encountered an error while processing your request."
    
    def _is_vague_health_problem(self, health_problem: str) -> bool:
        """
        Determine if a health problem description is too vague for specialist recommendation.
        
        Args:
            health_problem: The health problem description
            
        Returns:
            Boolean indicating if the health problem is too vague
        """
        vague_terms = [
            "a health issue", "health issue", "health problem", "symptoms", "health", 
            "illness", "sick", "not feeling well", "unwell", "problem", "issue",
            "medical condition", "condition", "something", "medical issue", "something wrong"
        ]
        
        health_problem_lower = health_problem.lower()
        
        # Check if the health problem is one of the vague terms
        if health_problem_lower in vague_terms:
            return True
            
        # Check if it's just 1-2 words that are very general
        word_count = len(health_problem_lower.split())
        if word_count <= 2:
            for vague_term in vague_terms:
                if vague_term in health_problem_lower:
                    return True
        
        return False
        
    def _is_doctor_availability_request(self, query: str) -> bool:
        """
        Determine if the user is asking to see available doctors after a specialist recommendation.
        
        Args:
            query: The user query to check
            
        Returns:
            Boolean indicating if the query is about doctor availability
        """
        query_lower = query.lower().strip()
        
        # Check for confirmation responses to see doctors
        if query_lower in ["yes", "yeah", "sure", "ok", "okay", "show me", "yes please", "show doctors"]:
            # Check if previous exchange was a specialist recommendation
            if len(self.conversation_history) >= 2:
                last_agent_msg = self.conversation_history[-2]["content"].lower()
                if "specialist" in last_agent_msg and ("reply with 'yes'" in last_agent_msg or "would you like to see available" in last_agent_msg):
                    # Extract the specialty from the last message if possible
                    if hasattr(self, 'specialty_memory') and self.specialty_memory:
                        for specialty_name in self.specialty_memory:
                            if specialty_name.lower() in last_agent_msg:
                                # Update the last recommended specialty
                                self.last_recommended_specialty = specialty_name
                                logger.info(f"Updated last recommended specialty to '{specialty_name}' based on conversation context")
                                break
                    return True
        
        # Check for explicit doctor availability requests (only after specialty selection)
        # These patterns should only match when user is asking to see available doctors,
        # not when they're describing their health problem
        availability_patterns = [
            r"(show|see|view|get).+(available).+(doctor|specialist)",
            r"(show|see|view|get).+(doctor|specialist).+(available)",
            r"(find|search).+(available).+(doctor|specialist)",
            r"(available).+(doctor|specialist)",
            r"(who|which).+(doctor|specialist).+(available)",
            r"(where|how).+(book|see).+(available).+(doctor|specialist)"
        ]
        
        # Only check patterns if we have specialties in memory (after recommendations)
        if hasattr(self, 'specialty_memory') and self.specialty_memory:
            for pattern in availability_patterns:
                if re.search(pattern, query_lower):
                    return True
                    
        return False

    def _is_list_all_specialties_query(self, query: str) -> bool:
        """
        Determine if a query is specifically asking for a list of all specialties.
        
        Args:
            query: The user query to check
            
        Returns:
            Boolean indicating if the query is asking for all specialties
        """
        query_lower = query.lower()
        
        # Direct patterns for requesting lists
        list_patterns = [
            r"(list|show|tell|give|what).+(all|every|available).+(specialt|doctor|department)",
            r"(what|which).+(specialt|doctor|department).+(available|have|offer)",
            r"show me.+(all|available).+(specialt|doctor)",
            r"(all|complete).+(list).+(specialt|department|doctor)",
            r"(what kind|what type).+doctor"
        ]
        
        for pattern in list_patterns:
            if re.search(pattern, query_lower):
                return True
                
        # Check for specific list-requesting terms
        list_terms = [
            "list all", "all specialists", "all departments", "all specialties", 
            "available specialties", "available doctors", "what specialties", 
            "which specialties", "what departments", "all your doctors", 
            "see all", "show all", "list of"
        ]
        
        for term in list_terms:
            if term in query_lower:
                return True
                
        return False 

    def _store_specialty_mapping(self, specialty_name: str, specialty_id: str) -> None:
        """
        Store a direct mapping between specialty name and ID without making API calls.
        
        Args:
            specialty_name: The name of the specialty
            specialty_id: The ID of the specialty
        """
        if not specialty_name or not specialty_id:
            return
            
        # Initialize specialty memory if it doesn't exist
        if not hasattr(self, 'specialty_memory'):
            self.specialty_memory = {}
            
        # Store the mapping
        self.specialty_memory[specialty_name] = specialty_id
        # Track this as the last recommended specialty
        self.last_recommended_specialty = specialty_name
        logger.info(f"Stored direct specialty mapping: '{specialty_name}' => ID '{specialty_id}'")

    def _is_specialty_selection_query(self, query: str) -> bool:
        """
        Check if the query is selecting one of the recommended specialties.
        
        Args:
            query: The user's input
            
        Returns:
            True if the query appears to be selecting a specialty, False otherwise
        """
        try:
            # Check if the message is a structured specialty selection via button click
            try:
                # Try to parse as JSON
                import json
                message_data = json.loads(query)
                if isinstance(message_data, dict) and message_data.get("message") == "specialty_selected":
                    # This is a structured specialty selection
                    if "specialty_id" in message_data:
                        logger.info(f"Detected structured specialty selection: {message_data}")
                        return True
            except (json.JSONDecodeError, TypeError):
                # Not JSON, continue with text-based detection
                pass
                
            # If we recently recommended specialties and have specialties in memory
            if not self.specialty_memory:
                logger.debug("No specialties in memory")
                return False
                
            # Define patterns for specialty selection
            select_patterns = [
                r"(?:I want|I'd like|I would like|let'?s go with|let'?s choose|choose|select|pick|I'?ll take|I'?ll go with|I pick|I select|I choose|go with|ok|okay) (?:the )?(.*?)(?: please| specialist| department| specialty| doctor|$)",
                r"(?:I prefer|I'?m interested in|interested in|I need) (?:a |an |the )?(.*?)(?: please| specialist| department| specialty| doctor|$)",
                r"(?:Show me|Tell me about|What about|How about|Can I see|I want to see) (?:the |more about |more info on |more information on |more details on |)?(.*?)(?: please| specialist| department| specialty| doctor|$)",
                r"^(.*?)(?:,? please| specialist| department| specialty| doctor|$)",
                r"(?:Yes[,.]?|Yeah[,.]?|Sure[,.]?|Ok[,.]?|Okay[,.]?) (?:let'?s go with |I'?ll take |I want |I'?d like |give me |show me |)(.*?)(?:\.|$)"
            ]
            
            query_lower = query.lower()
            
            # Check for specialty name match in the query
            for specialty_name in self.specialty_memory.keys():
                if specialty_name.lower() in query_lower:
                    logger.info(f"Found direct specialty match: {specialty_name}")
                    return True
                    
            # Check for patterns indicating specialty selection
            for pattern in select_patterns:
                matches = re.findall(pattern, query, re.IGNORECASE)
                if matches:
                    for match in matches:
                        match = match.strip()
                        logger.debug(f"Pattern match: '{match}'")
                        
                        # Skip very short matches
                        if len(match) < 3:
                            continue
                            
                        # Skip generic terms
                        if match.lower() in ["one", "it", "that", "this", "them", "yes", "no", "options", "first", "second", "third"]:
                            continue
                            
                        # Check if this matches any specialty in memory
                        for specialty_name in self.specialty_memory.keys():
                            specialty_lower = specialty_name.lower()
                            match_lower = match.lower()
                            
                            # Check for direct or partial match
                            if match_lower in specialty_lower or specialty_lower in match_lower:
                                logger.info(f"Found specialty selection: '{match}' matches '{specialty_name}'")
                                return True
                                
                        # If we have a reasonable match that doesn't match known specialties,
                        # it could still be a specialty selection
                        if len(match) > 5:
                            logger.info(f"Potential specialty selection: '{match}'")
                            return True
                    
            # Last resort: check if this might be a specialty selection based on context
            context_triggers = ["see", "chose", "choose", "specialist", "specialty", "doctor", "appointment"]
            if any(trigger in query_lower for trigger in context_triggers) and self.last_recommended_specialty:
                history_entries = [h["content"] for h in self.conversation_history[-4:] if h["role"] == "assistant"]
                recommending_specialties = any("recommend" in entry.lower() and "specialist" in entry.lower() for entry in history_entries)
                if recommending_specialties:
                    logger.info("Context suggests specialty selection")
                    return True
            
            return False
        except Exception as e:
            logger.error(f"Error in _is_specialty_selection_query: {str(e)}")
            return False
            
    def _extract_selected_specialty(self, query: str) -> str:
        """
        Extracts the specialty name from a user's selection query.
        
        Args:
            query: The user's input query that appears to be a specialty selection
            
        Returns:
            The extracted specialty name or None if no match is found
        """
        query_lower = query.lower().strip()
        
        # Log all ʼ for debugging
        logger.info(f"Available specialties in memory: {list(self.specialty_memory.keys())}")
        
        # Remove common prefixes to get to the actual specialty name
        prefixes = [
            "i choose ", "i select ", "i pick ", "i want ", "i prefer ", 
            "let's go with ", "go with ", "choose ", "select ", "pick ",
            "i'd like to see ", "i would like to see ", "show me ",
            "i want to see ", "let me see ", "i'd like ", "i would like "
        ]
        
        for prefix in prefixes:
            if query_lower.startswith(prefix):
                query_lower = query_lower[len(prefix):].strip()
                break
        
        # Now match against our specialty names
        best_match = None
        best_match_score = 0
        
        # Common variations and abbreviations
        variations = {
            "ortho": ["orthopaedic", "orthopedic", "orthopaedics", "orthopedics"],
            "cardio": ["cardiology", "cardiovascular"],
            "neuro": ["neurology", "neuroscience", "neurological"],
            "gastro": ["gastroenterology", "gastrointestinal"],
            "psych": ["psychiatry", "psychological", "psychology", "mental health"],
            "gyn": ["gynecology", "gynaecology", "obgyn", "obstetrics"]
        }
        
        # Check for common variations
        for base, variants in variations.items():
            if base in query_lower or any(v in query_lower for v in variants):
                logger.info(f"Detected common variation: {base} in '{query_lower}'")
                query_lower = query_lower.replace(base, "")  # Remove the base term for better matching
                # Check all specialties for a match with these variations
                for specialty_name in self.specialty_memory.keys():
                    specialty_lower = specialty_name.lower()
                    if base in specialty_lower or any(v in specialty_lower for v in variants):
                        logger.info(f"Found specialty match through variation: '{specialty_name}' for '{query}'")
                        return specialty_name
        
        # Standard matching approach
        for specialty_name in self.specialty_memory.keys():
            specialty_lower = specialty_name.lower()
            
            # Try different matching strategies and score them
            score = 0
            
            # Exact match gets highest score
            if query_lower == specialty_lower:
                score = 100
            # Specialty is fully contained in query
            elif specialty_lower in query_lower:
                score = 90
            # Query is fully contained in specialty
            elif query_lower in specialty_lower:
                score = 80
            # Check if query words are contained in specialty
            else:
                query_words = query_lower.split()
                specialty_words = specialty_lower.split()
                
                matching_words = 0
                for qword in query_words:
                    if any(qword in sword for sword in specialty_words):
                        matching_words += 1
                
                if matching_words > 0:
                    score = (matching_words / len(query_words)) * 70
            
            # Update best match if we have a better score
            if score > best_match_score:
                best_match_score = score
                best_match = specialty_name
                
                # If we have a very high confidence match, return immediately
                if score >= 90:
                    logger.info(f"High confidence match: '{query}' -> '{specialty_name}'")
                    return specialty_name
        
        # Return the best match if it has a reasonable score
        if best_match_score > 40:  # Lower threshold for better matching
            logger.info(f"Best match for '{query}': '{best_match}' (score: {best_match_score})")
            return best_match
        else:
            logger.info(f"No good specialty match found for '{query}'")
            return None

    def _decide_response(self, user_query: str, reasoning: Dict[str, Any], 
                          action_result: Dict[str, Any], observation: Dict[str, Any]) -> str:
        """
        Decide on the final response to the user based on the observation.
        
        Args:
            user_query: Original user query
            reasoning: Output from the reasoning step
            action_result: Result from the action step
            observation: Processed observation
            
        Returns:
            Final response string to the user
        """
        logger.info("\n=== FINAL REASONING ===")
        logger.info("Generating final answer based on observation...")
        
        try:
            # Construct prompt for the LLM
            final_prompt = self._construct_final_answer_prompt(
                user_query, 
                reasoning, 
                action_result, 
                observation
            )
            
            # Call LLM for final answer
            final_response = self._call_llm(final_prompt)
            final_answer = self._extract_final_answer(final_response)
            
            # Special handle for specialty recommendations - extract data from observation to build buttons
            obs_data = observation.get("observation", {})
            if isinstance(obs_data, dict) and "filtered_suggestions" in obs_data:
                filtered_suggestions = obs_data.get("filtered_suggestions", [])
                if filtered_suggestions and len(filtered_suggestions) > 0:
                    # Log the number of filtered suggestions and their names
                    specialty_names = [s.get("specialty", "Unknown") for s in filtered_suggestions]
                    logger.info(f"Processing {len(filtered_suggestions)} filtered suggestions: {specialty_names}")
                    
                    # Format the answer as structured text with bullet points
                    health_problem = obs_data.get("health_problem", "your symptoms")
                    emergency_advice = obs_data.get("emergency_advice", "")
                    
                    # Adjust message based on number of specialties
                    if len(filtered_suggestions) > 1:
                        formatted_answer = f"For {health_problem}, we recommend the following specialties (you can select any one):\n\n"
                    else:
                        formatted_answer = f"For {health_problem}, we recommend the following specialty:\n\n"
                    
                    # Add each specialty as a bullet point
                    for suggestion in filtered_suggestions:
                        specialty_name = suggestion.get("specialty", "Unknown")
                        reason = suggestion.get("reason", "No reason provided")
                        formatted_answer += f"• **{specialty_name}**: {reason}\n"
                        logger.info(f"Added specialty to response: {specialty_name}")
                    
                    # Add emergency advice if present
                    if emergency_advice:
                        logger.info(f"Adding emergency advice to response: {emergency_advice}")
                        # Don't add extra emoji if emergency advice already has them
                        if emergency_advice.startswith('🚨') or emergency_advice.startswith('⚠️'):
                            formatted_answer += f"\n{emergency_advice}\n"
                        else:
                            formatted_answer += f"\n⚠️ {emergency_advice}\n"
                        logger.info(f"Final formatted answer with emergency advice: {formatted_answer}")
                    
                    # Let user know they can select a specialty - adjust based on count
                    if len(filtered_suggestions) > 1:
                        formatted_answer += "\nPlease select the specialty you prefer:"
                    else:
                        formatted_answer += "\nPlease select the specialty to explore available doctors:"
                    
                    # Replace the final answer with our more structured version
                    final_answer = formatted_answer
                    
                    # Store memory that we're asking user to select a specialty
                    self._store_memory(
                        memory_type="interaction_state",
                        content=f"Asked user to select a specialty for health problem: {health_problem}",
                        importance=2
                    )
                    
                    # Log the number of specialties we're recommending
                    logger.info(f"Recommending {len(filtered_suggestions)} specialties for '{health_problem}'")
                    
                    return final_answer
            
            # Send WebSocket response if available
            logger.info(f"Sent WebSocket response: {final_answer[:50]}...")
            
            return final_answer
            
        except Exception as e:
            logger.error(f"Error in final reasoning: {str(e)}")
            
            # Fallback response based on observation data
            obs_data = observation.get("observation", {})
            
            # Handle specialist recommendations
            if isinstance(obs_data, dict) and "filtered_suggestions" in obs_data:
                filtered_suggestions = obs_data.get("filtered_suggestions", [])
                health_problem = obs_data.get("health_problem", "your health issue")
                emergency_advice = obs_data.get("emergency_advice", "")
                
                if filtered_suggestions:
                    if len(filtered_suggestions) == 1:
                        # Single specialty recommendation
                        specialty = filtered_suggestions[0].get("specialty", "specialist")
                        reason = filtered_suggestions[0].get("reason", "")
                        final_answer = f"For {health_problem}, we recommend a {specialty} specialist. {reason}\n\nPlease click the button below to see available doctors:"
                    else:
                        # Multiple specialty recommendations with bullet points
                        final_answer = f"For {health_problem}, we recommend the following specialties:\n\n"
                        
                        # Add each specialty as a bullet point with its reason
                        for suggestion in filtered_suggestions:
                            specialty = suggestion.get("specialty", "Unknown")
                            reason = suggestion.get("reason", "No reason provided")
                            final_answer += f"• **{specialty}**: {reason}\n"
                        
                        final_answer += "\nPlease select a specialty to explore available doctors:"
                    
                    # Add emergency advice if present
                    if emergency_advice:
                        logger.info(f"Adding emergency advice to fallback response: {emergency_advice}")
                        # Don't add extra emoji if emergency advice already has them
                        if emergency_advice.startswith('🚨') or emergency_advice.startswith('⚠️'):
                            final_answer += f"\n\n{emergency_advice}"
                        else:
                            final_answer += f"\n\n⚠️ {emergency_advice}"
                        
                    return final_answer
            
            # Handle doctor availability
            elif isinstance(obs_data, dict) and obs_data.get("type") == "doctor_availability":
                doctors = obs_data.get("available_doctors", [])
                doctor_count = obs_data.get("doctor_count", 0)
                specialty_name = obs_data.get("specialty_name", "Unknown Specialty")
                date_range = obs_data.get("date_range", "Unknown dates")
                
                if doctor_count > 0 and doctors:
                    # Format available doctors - each session is now a separate entry
                    final_answer = f"Here are the available {specialty_name} doctors:\n\n"
                    
                    for i, session in enumerate(doctors):  # Each entry is now a session
                        name = session.get("name", "Unknown Doctor")
                        start_time = session.get("start_time_display", "Unknown time")
                        available_slots = session.get("available_slots", 0)
                        session_desc = session.get("session_desc", "")
                        resource_id = session.get("resource_id", "")
                        session_id = session.get("session_id", "")
                        session_date = session.get("session_date", "")
                        
                        # Format the session entry with embedded data for frontend
                        final_answer += f"• **Dr. {name}**\n"
                        final_answer += f"  {start_time}\n"
                        final_answer += f"  {available_slots} slots available\n"
                        final_answer += f"  RESOURCEID: {resource_id}\n"
                        final_answer += f"  SESSIONID: {session_id}\n"
                        final_answer += f"  SESSIONDATE: {session_date}\n\n"
                    
                    final_answer += "Please select a doctor to see available slots."
                else:
                    # Extract the requested date from date_range for better messaging
                    date_from = date_range.split(" to ")[0] if " to " in date_range else date_range
                    try:
                        # Format the date nicely
                        date_obj = pendulum.parse(date_from)
                        formatted_date = date_obj.format("MMMM D, YYYY")
                        final_answer = f"I'm sorry, but we couldn't find any {specialty_name} doctors available for {formatted_date}. Please try a different date or specialty."
                    except:
                        final_answer = f"I'm sorry, but we couldn't find any {specialty_name} doctors available for the requested date. Please try a different date or specialty."
                
                return final_answer
            
            # Handle specialty lists
            elif isinstance(obs_data, dict) and obs_data.get("type") == "specialty_list":
                specialty_names = obs_data.get("formatted_list", [])
                specialty_count = obs_data.get("specialty_count", 0)
                
                if specialty_count > 0:
                    final_answer = f"Here are the medical specialties available at our hospital:\n\n"
                    
                    # Format the list in columns for better readability if there are many
                    if specialty_count > 15:
                        # Create columns for better readability
                        col_size = (specialty_count + 2) // 3
                        cols = []
                        
                        for i in range(0, specialty_count, col_size):
                            col_items = specialty_names[i:i+col_size]
                            cols.append("• " + "\n• ".join(col_items))
                        
                        final_answer += "\n\n".join(cols)
                    else:
                        final_answer += "• " + "\n• ".join(specialty_names)
                    
                    final_answer += "\n\nWould you like me to help you find a specialist for a specific health concern?"
                else:
                    final_answer = "We're unable to retrieve any specialties matching your query. Is there a specific health concern you'd like help with?"
                
                return final_answer
            
            # Generic fallback
            return "I processed your request and found relevant information. Is there anything specific you'd like to know about our specialties or available doctors?"
            
        except Exception as nested_e:
            logger.error(f"Error in fallback handling: {str(nested_e)}")
            return "I'm sorry, I'm having trouble processing your request at the moment. Could you try asking again or rephrase your question?" 