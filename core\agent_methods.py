"""
Additional methods for the ReAct Agent.
This file contains the reasoning, action, and observation methods.
"""

import json
import logging
import re
import pendulum
from typing import Dict, List, Any, Optional

logger = logging.getLogger("AgentMethods")

class AgentMethodsMixin:
    """Mixin class containing additional methods for the ReAct Agent."""
    
    def _is_date_input(self, user_query: str) -> bool:
        """
        Check if the user's query contains a date input.
        
        Args:
            user_query: The user's input query
            
        Returns:
            True if query contains a date, False otherwise
        """
        try:
            # Check for common date patterns
            date_patterns = [
                r'\d{4}-\d{2}-\d{2}',  # YYYY-MM-DD
                r'\d{2}/\d{2}/\d{4}',  # MM/DD/YYYY or DD/MM/YYYY
                r'\d{2}-\d{2}-\d{4}',  # DD-MM-YYYY or MM-DD-YYYY
                r'\d{1,2}/\d{1,2}/\d{4}',  # M/D/YYYY or D/M/YYYY
                r'\d{1,2}-\d{1,2}-\d{4}',  # D-M-YYYY or M-D-YYYY
            ]
            
            for pattern in date_patterns:
                if re.search(pattern, user_query.strip()):
                    return True
            
            # Check for day of week patterns
            day_patterns = [
                r'\b(monday|tuesday|wednesday|thursday|friday|saturday|sunday)\b',
                r'\b(mon|tue|wed|thu|fri|sat|sun)\b',
                r'\b(today|tomorrow|tommorrow|tomorow|tommorow|next week|this week)\b',  # Include misspellings
                r'\bon\s+(\d{1,2})(st|nd|rd|th)?\b',  # "on 8th", "on 15"
                r'\b(\d{1,2})(st|nd|rd|th)?\s+(january|february|march|april|may|june|july|august|september|october|november|december|jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\b',  # "8th Jan"
                r'\b(january|february|march|april|may|june|july|august|september|october|november|december|jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\s+(\d{1,2})(st|nd|rd|th)?\b',  # "Jan 8th"
            ]
            
            query_lower = user_query.lower()
            for pattern in day_patterns:
                if re.search(pattern, query_lower):
                    return True
                    
            return False
            
        except Exception as e:
            logger.error(f"Error checking date input: {str(e)}")
            return False
    
    def _is_greeting(self, user_query: str) -> bool:
        """
        Check if the user's query is a greeting or simple question.
        
        Args:
            user_query: The user's input query
            
        Returns:
            True if query is a greeting, False otherwise
        """
        query_lower = user_query.lower().strip()
        
        # Common greetings
        greetings = [
            "hello", "hi", "hey", "good morning", "good afternoon", "good evening",
            "how are you", "what's up", "greetings", "salutations"
        ]
        
        # Simple questions that don't require tools
        simple_questions = [
            "what can you do", "what do you do", "how can you help", "help me",
            "what services", "what is this", "who are you", "what are you"
        ]
        
        # Check for exact matches or if query starts with these phrases
        for greeting in greetings + simple_questions:
            if query_lower == greeting or query_lower.startswith(greeting):
                return True
        
        return False
    
    def _get_greeting_response(self, user_query: str) -> str:
        """
        Get an appropriate greeting response.
        
        Args:
            user_query: The user's greeting query
            
        Returns:
            Appropriate greeting response
        """
        query_lower = user_query.lower().strip()
        
        if any(word in query_lower for word in ["hello", "hi", "hey", "good morning", "good afternoon", "good evening"]):
            return "Hello! I'm your SmartClinic assistant. I can help you find the right medical specialist for your health concerns and check doctor availability. What can I help you with today?"
        
        elif any(phrase in query_lower for phrase in ["what can you do", "what do you do", "how can you help", "help me"]):
            return "I can help you with:\n• Finding the right medical specialist for your health concerns\n• Checking doctor availability and scheduling appointments\n• Providing information about our hospital specialties\n\nJust tell me about your symptoms or what type of specialist you're looking for!"
        
        elif any(phrase in query_lower for phrase in ["what services", "what is this"]):
            return "This is SmartClinic's medical assistant. I help patients find appropriate specialists and book appointments based on their health concerns. How can I assist you today?"
        
        elif any(phrase in query_lower for phrase in ["who are you", "what are you"]):
            return "I'm your SmartClinic AI assistant, designed to help you navigate our healthcare services. I can recommend specialists based on your symptoms and help you find available appointments. What health concern can I help you with?"
        
        else:
            return "Hello! I'm here to help you with medical specialist recommendations and appointment scheduling. What can I assist you with today?"
    
    def _is_specialty_selection_query(self, user_query: str) -> bool:
        """
        Check if the user is selecting a specialty from a previous recommendation.
        
        Args:
            user_query: The user's input query
            
        Returns:
            True if this appears to be a specialty selection
        """
        query_lower = user_query.lower().strip()
        
        # Check if it's a structured JSON message (handled elsewhere)
        try:
            parsed = json.loads(user_query)
            if isinstance(parsed, dict) and parsed.get("message") == "specialty_selected":
                return True
        except (json.JSONDecodeError, TypeError):
            pass
        
        # Check if the query matches any specialty in memory
        if hasattr(self, 'specialty_memory') and self.specialty_memory:
            for specialty_name in self.specialty_memory.keys():
                if specialty_name.lower() in query_lower or query_lower in specialty_name.lower():
                    return True
        
        # Check for common specialty selection patterns
        selection_patterns = [
            r'\b(i choose|i select|i want|i pick|i prefer)\b',
            r'\b(go with|book with|see)\b',
            r'\b(option \d+|number \d+|\d+st|\d+nd|\d+rd|\d+th)\b'
        ]
        
        for pattern in selection_patterns:
            if re.search(pattern, query_lower):
                return True
        
        return False
    
    def _extract_selected_specialty(self, user_query: str) -> Optional[str]:
        """
        Extract the selected specialty from user query.
        
        Args:
            user_query: The user's input query
            
        Returns:
            Selected specialty name or None if not found
        """
        query_lower = user_query.lower().strip()
        
        # Check against known specialties in memory
        if hasattr(self, 'specialty_memory') and self.specialty_memory:
            # First try exact matches
            for specialty_name in self.specialty_memory.keys():
                if specialty_name.lower() == query_lower:
                    return specialty_name
            
            # Then try partial matches
            for specialty_name in self.specialty_memory.keys():
                if specialty_name.lower() in query_lower or query_lower in specialty_name.lower():
                    return specialty_name
        
        return None
    
    def _is_list_all_specialties_query(self, user_query: str) -> bool:
        """
        Check if the user is requesting a full list of all specialties.
        
        Args:
            user_query: The user's input query
            
        Returns:
            True if this is a request for all specialties
        """
        query_lower = user_query.lower().strip()
        
        list_patterns = [
            r'\b(list all|show all|all specialties|all departments)\b',
            r'\b(what specialties|which specialties|available specialties)\b',
            r'\b(full list|complete list|entire list)\b',
            r'\b(all doctors|all specialists|all departments)\b'
        ]
        
        for pattern in list_patterns:
            if re.search(pattern, query_lower):
                return True
        
        return False
    
    def _is_health_problem_query(self, user_query: str) -> bool:
        """
        Check if the user is describing a health problem or symptom.
        
        Args:
            user_query: The user's input query
            
        Returns:
            True if this appears to be a health problem query
        """
        query_lower = user_query.lower().strip()
        
        # Check for health problem keywords
        for keyword in self.health_problem_keywords:
            if keyword in query_lower:
                return True
        
        # Check for common health problem patterns
        health_patterns = [
            r'\bi have\b',
            r'\bi am (feeling|experiencing|suffering)\b',
            r'\bmy .* (hurts|aches|is painful)\b',
            r'\bwhat specialist for\b',
            r'\bwho should i see for\b',
            r'\bwhich doctor for\b'
        ]
        
        for pattern in health_patterns:
            if re.search(pattern, query_lower):
                return True
        
        return False
    
    def _extract_health_problem(self, user_query: str) -> str:
        """
        Extract the health problem description from user query.
        
        Args:
            user_query: The user's input query
            
        Returns:
            Extracted health problem description
        """
        # For now, return the entire query as the health problem
        # This could be enhanced with more sophisticated NLP
        return user_query.strip()
    
    def _is_vague_health_problem(self, health_problem: str) -> bool:
        """
        Check if the health problem description is too vague.
        
        Args:
            health_problem: The health problem description
            
        Returns:
            True if the problem is too vague for meaningful recommendation
        """
        problem_lower = health_problem.lower().strip()
        
        vague_terms = [
            "sick", "unwell", "not feeling well", "something wrong",
            "health issue", "medical problem", "see a doctor",
            "check up", "general", "overall health"
        ]
        
        # If the problem is very short or only contains vague terms
        if len(problem_lower.split()) <= 3:
            for term in vague_terms:
                if term in problem_lower:
                    return True
        
        return False
