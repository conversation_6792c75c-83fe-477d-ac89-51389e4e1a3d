#!/usr/bin/env python3
"""
Cleanup script to remove unwanted files from the project.
Run this before creating a ZIP file for distribution.
"""

import os
import shutil
import glob

def remove_pycache():
    """Remove all __pycache__ directories."""
    print("🧹 Removing __pycache__ directories...")
    for root, dirs, files in os.walk('.'):
        for dir_name in dirs:
            if dir_name == '__pycache__':
                pycache_path = os.path.join(root, dir_name)
                print(f"  Removing: {pycache_path}")
                shutil.rmtree(pycache_path)

def remove_pyc_files():
    """Remove all .pyc files."""
    print("🧹 Removing .pyc files...")
    pyc_files = glob.glob('**/*.pyc', recursive=True)
    for pyc_file in pyc_files:
        print(f"  Removing: {pyc_file}")
        os.remove(pyc_file)

def remove_env_files():
    """Remove environment files if they exist."""
    print("🧹 Removing environment files...")
    env_files = ['.env', '.env.local', '.env.development', '.env.production']
    for env_file in env_files:
        if os.path.exists(env_file):
            print(f"  Removing: {env_file}")
            os.remove(env_file)

def remove_ide_files():
    """Remove IDE-specific files and directories."""
    print("🧹 Removing IDE files...")
    ide_items = ['.vscode', '.idea', '*.swp', '*.swo', '.DS_Store', 'Thumbs.db']
    
    for item in ide_items:
        if '*' in item:
            # Handle glob patterns
            matches = glob.glob(item, recursive=True)
            for match in matches:
                print(f"  Removing: {match}")
                os.remove(match)
        else:
            # Handle directories and files
            if os.path.exists(item):
                if os.path.isdir(item):
                    print(f"  Removing directory: {item}")
                    shutil.rmtree(item)
                else:
                    print(f"  Removing file: {item}")
                    os.remove(item)

def remove_log_files():
    """Remove log files."""
    print("🧹 Removing log files...")
    log_files = glob.glob('**/*.log', recursive=True)
    for log_file in log_files:
        print(f"  Removing: {log_file}")
        os.remove(log_file)

def remove_temp_files():
    """Remove temporary files."""
    print("🧹 Removing temporary files...")
    temp_patterns = ['**/*.tmp', '**/*.temp', '**/temp/', '**/logs/']
    
    for pattern in temp_patterns:
        matches = glob.glob(pattern, recursive=True)
        for match in matches:
            if os.path.isdir(match):
                print(f"  Removing directory: {match}")
                shutil.rmtree(match)
            else:
                print(f"  Removing file: {match}")
                os.remove(match)

def main():
    """Run all cleanup operations."""
    print("🧹 Starting project cleanup...")
    print("=" * 50)
    
    try:
        remove_pycache()
        remove_pyc_files()
        remove_env_files()
        remove_ide_files()
        remove_log_files()
        remove_temp_files()
        
        print("=" * 50)
        print("✅ Cleanup completed successfully!")
        print("\n📁 Your project is now clean and ready for ZIP creation.")
        print("\n📋 Remaining files:")
        
        # Show remaining files
        for root, dirs, files in os.walk('.'):
            # Skip hidden directories
            dirs[:] = [d for d in dirs if not d.startswith('.')]
            
            level = root.replace('.', '').count(os.sep)
            indent = ' ' * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            
            subindent = ' ' * 2 * (level + 1)
            for file in files:
                if not file.startswith('.'):
                    print(f"{subindent}{file}")
        
    except Exception as e:
        print(f"❌ Error during cleanup: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Ready to create ZIP file!")
    else:
        print("\n❌ Cleanup failed. Please check the errors above.")
